"""
测试数据模型
"""

import pytest
import numpy as np
from mcp_semantic_splitter.core.models import (
    SplitterConfig,
    FeatureVector,
    CellPosition,
    CellRange,
    LogicalUnit,
    UnitType,
    Segment,
    SegmentType,
    SplitResult,
)


class TestSplitterConfig:
    """测试分割器配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = SplitterConfig()
        assert config.semantic_model == "paraphrase-multilingual-mpnet-base-v2"
        assert config.min_segment_size == 5
        assert config.boundary_threshold == 0.6
        assert config.device == "cpu"
    
    def test_weight_validation(self):
        """测试权重验证"""
        config = SplitterConfig()
        # 默认权重应该总和为1
        config.validate_weights()  # 不应该抛出异常
        
        # 修改权重使总和不为1
        config.semantic_weight = 0.5
        with pytest.raises(ValueError):
            config.validate_weights()
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = SplitterConfig(
            min_segment_size=10,
            boundary_threshold=0.8,
            semantic_weight=0.5,
            layout_weight=0.3,
            format_weight=0.1,
            datatype_weight=0.05,
            unittype_weight=0.05
        )
        assert config.min_segment_size == 10
        assert config.boundary_threshold == 0.8
        config.validate_weights()  # 应该通过验证


class TestCellPosition:
    """测试单元格位置"""
    
    def test_cell_position_creation(self):
        """测试单元格位置创建"""
        pos = CellPosition(row=5, column=3)
        assert pos.row == 5
        assert pos.column == 3
        assert str(pos) == "R5C3"


class TestCellRange:
    """测试单元格范围"""
    
    def test_cell_range_creation(self):
        """测试单元格范围创建"""
        start = CellPosition(row=1, column=1)
        end = CellPosition(row=3, column=5)
        range_obj = CellRange(start=start, end=end)
        
        assert range_obj.row_count == 3
        assert range_obj.column_count == 5
        assert range_obj.cell_count == 15
        assert str(range_obj) == "R1C1:R3C5"


class TestFeatureVector:
    """测试特征向量"""
    
    def test_feature_vector_creation(self):
        """测试特征向量创建"""
        semantic = np.random.rand(384)
        layout = np.array([2, 3, 6, 0, 1, 1], dtype=np.float32)
        format_feat = np.array([12.0, 0.0, 0.0, 255.0, 255.0, 255.0, 0.0], dtype=np.float32)
        datatype = np.array([0.6, 0.3, 0.1, 0.0], dtype=np.float32)
        unittype = np.array([1.0, 0.0, 0.0, 0.0, 0.0], dtype=np.float32)
        
        feature_vector = FeatureVector(
            semantic=semantic,
            layout=layout,
            format=format_feat,
            datatype=datatype,
            unittype=unittype
        )
        
        assert feature_vector.semantic.shape == (384,)
        assert feature_vector.layout.shape == (6,)
        assert feature_vector.format.shape == (7,)
        assert feature_vector.datatype.shape == (4,)
        assert feature_vector.unittype.shape == (5,)
    
    def test_feature_vector_to_dict(self):
        """测试特征向量转换为字典"""
        semantic = np.array([0.1, 0.2, 0.3])
        layout = np.array([1, 2, 3])
        format_feat = np.array([12.0, 0.0])
        datatype = np.array([0.5, 0.5])
        unittype = np.array([1.0, 0.0])
        
        feature_vector = FeatureVector(
            semantic=semantic,
            layout=layout,
            format=format_feat,
            datatype=datatype,
            unittype=unittype
        )
        
        feature_dict = feature_vector.to_dict()
        assert "semantic" in feature_dict
        assert "layout" in feature_dict
        assert feature_dict["semantic"] == [0.1, 0.2, 0.3]


class TestLogicalUnit:
    """测试逻辑单元"""
    
    def test_logical_unit_creation(self):
        """测试逻辑单元创建"""
        start = CellPosition(row=1, column=1)
        end = CellPosition(row=1, column=3)
        range_obj = CellRange(start=start, end=end)
        
        unit = LogicalUnit(
            unit_id="test_unit_1",
            unit_type=UnitType.ROW,
            range=range_obj,
            content={"A": "Name", "B": "Age", "C": "City"},
            metadata={"test": True}
        )
        
        assert unit.unit_id == "test_unit_1"
        assert unit.unit_type == UnitType.ROW
        assert unit.range.cell_count == 3
        assert unit.metadata["test"] is True
        assert "test_unit_1" in str(unit)


class TestSegment:
    """测试片段"""
    
    def test_segment_creation(self):
        """测试片段创建"""
        # 创建逻辑单元
        start = CellPosition(row=1, column=1)
        end = CellPosition(row=1, column=3)
        range_obj = CellRange(start=start, end=end)
        
        unit1 = LogicalUnit(
            unit_id="unit_1",
            unit_type=UnitType.ROW,
            range=range_obj,
            content={"A": "Name", "B": "Age", "C": "City"}
        )
        
        unit2 = LogicalUnit(
            unit_id="unit_2",
            unit_type=UnitType.ROW,
            range=range_obj,
            content={"A": "John", "B": "25", "C": "NYC"}
        )
        
        # 创建片段
        segment = Segment(
            segment_id="segment_1",
            segment_type=SegmentType.DATA_TABLE,
            source_location="Sheet1!A1:C2",
            units=[unit1, unit2],
            content=None
        )
        
        assert segment.segment_id == "segment_1"
        assert segment.segment_type == SegmentType.DATA_TABLE
        assert len(segment.units) == 2
        assert segment.total_cells == 6  # 2个单元 * 3个单元格
        assert "segment_1" in str(segment)


class TestSplitResult:
    """测试分割结果"""
    
    def test_split_result_creation(self):
        """测试分割结果创建"""
        result = SplitResult(
            file_path="/path/to/test.xlsx",
            sheet_name="Sheet1",
            segments=[],
            processing_time=1.5,
            metadata={"test": True}
        )
        
        assert result.file_path == "/path/to/test.xlsx"
        assert result.sheet_name == "Sheet1"
        assert result.processing_time == 1.5
        assert result.metadata["test"] is True
    
    def test_split_result_to_dict(self):
        """测试分割结果转换为字典"""
        # 创建一个简单的片段
        start = CellPosition(row=1, column=1)
        end = CellPosition(row=1, column=1)
        range_obj = CellRange(start=start, end=end)
        
        unit = LogicalUnit(
            unit_id="unit_1",
            unit_type=UnitType.BLOCK,
            range=range_obj,
            content="Test content"
        )
        
        segment = Segment(
            segment_id="segment_1",
            segment_type=SegmentType.PROSE,
            source_location="Sheet1!A1:A1",
            units=[unit],
            content="Test content"
        )
        
        result = SplitResult(
            file_path="/path/to/test.xlsx",
            sheet_name="Sheet1",
            segments=[segment],
            processing_time=1.5
        )
        
        result_dict = result.to_dict()
        assert result_dict["file_path"] == "/path/to/test.xlsx"
        assert result_dict["sheet_name"] == "Sheet1"
        assert len(result_dict["segments"]) == 1
        assert result_dict["segments"][0]["segment_id"] == "segment_1"
