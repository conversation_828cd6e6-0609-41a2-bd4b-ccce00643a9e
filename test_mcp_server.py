"""
测试MCP服务器的简化版本
"""

import asyncio
import json
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.server.models import InitializationOptions
from mcp.types import Tool, TextContent


async def test_simple_mcp_server():
    """测试简单的MCP服务器"""
    
    # 创建服务器
    server = Server("test-mcp-server")
    
    @server.list_tools()
    async def handle_list_tools():
        """列出工具"""
        return [
            Tool(
                name="test_tool",
                description="测试工具",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "测试消息"
                        }
                    },
                    "required": ["message"]
                }
            )
        ]
    
    @server.call_tool()
    async def handle_call_tool(name: str, arguments: dict):
        """处理工具调用"""
        if name == "test_tool":
            message = arguments.get("message", "Hello")
            return [TextContent(
                type="text",
                text=f"收到消息: {message}"
            )]
        else:
            return [TextContent(
                type="text",
                text=f"未知工具: {name}"
            )]
    
    print("启动测试MCP服务器...")
    print("服务器正在等待连接...")
    
    try:
        # 运行服务器
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="test-mcp-server",
                    server_version="0.1.0",
                    capabilities=server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities={}
                    )
                )
            )
    except Exception as e:
        print(f"服务器运行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_simple_mcp_server())
