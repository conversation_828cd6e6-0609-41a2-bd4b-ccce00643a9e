"""
MCP服务器实现

提供基于Model Context Protocol的表格语义分割服务。
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import tempfile
import os

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

from .core.splitter import SemanticSplitter
from .core.models import SplitterConfig
from .utils.logging_config import setup_logging
from .utils.validators import validate_file_path, validate_config
from .utils.formatters import format_results, create_summary_report

logger = logging.getLogger(__name__)

# 全局分割器实例
splitter: Optional[SemanticSplitter] = None


def create_server() -> Server:
    """创建MCP服务器实例"""
    server = Server("mcp-semantic-splitter")
    
    @server.list_resources()
    async def handle_list_resources() -> List[Resource]:
        """列出可用资源"""
        return [
            Resource(
                uri="config://current",
                name="当前配置",
                description="查看当前的分割器配置",
                mimeType="application/json",
            ),
            Resource(
                uri="help://usage",
                name="使用帮助",
                description="表格语义分割器的使用说明",
                mimeType="text/markdown",
            ),
        ]
    
    @server.read_resource()
    async def handle_read_resource(uri: str) -> str:
        """读取资源内容"""
        if uri == "config://current":
            if splitter:
                config_dict = vars(splitter.config)
                return json.dumps(config_dict, indent=2, ensure_ascii=False)
            else:
                return json.dumps({"error": "分割器未初始化"}, ensure_ascii=False)
        
        elif uri == "help://usage":
            return """# 表格语义分割器使用说明

## 功能概述
本工具提供智能的Excel表格语义分割功能，能够：
- 自动识别表格中的逻辑结构
- 基于多维特征进行智能分割
- 支持多工作表处理
- 提供结构化的分割结果

## 可用工具

### 1. split_excel_file
分割Excel文件中的表格

**参数：**
- `file_path` (必需): Excel文件路径
- `sheet_name` (可选): 指定工作表名称
- `config` (可选): 分割配置参数

**示例：**
```json
{
  "file_path": "/path/to/your/file.xlsx",
  "sheet_name": "Sheet1",
  "config": {
    "min_segment_size": 5,
    "boundary_threshold": 0.6
  }
}
```

### 2. analyze_file_structure
分析文件结构（不进行分割）

**参数：**
- `file_path` (必需): Excel文件路径

### 3. configure_splitter
配置分割器参数

**参数：**
- `config` (必需): 配置参数字典

## 配置参数说明

- `semantic_model`: 语义模型名称
- `min_segment_size`: 最小片段大小
- `boundary_threshold`: 边界检测阈值
- `semantic_weight`: 语义特征权重
- `layout_weight`: 布局特征权重
- `format_weight`: 格式特征权重
- `datatype_weight`: 数据类型特征权重
- `unittype_weight`: 单元类型特征权重

## 输出格式

分割结果包含：
- 片段列表，每个片段包含类型、位置、内容等信息
- 处理统计信息
- 质量分析结果
"""
        
        else:
            raise ValueError(f"未知资源: {uri}")
    
    @server.list_tools()
    async def handle_list_tools() -> List[Tool]:
        """列出可用工具"""
        return [
            Tool(
                name="split_excel_file",
                description="分割Excel文件中的表格，提供智能的语义分割功能",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "Excel文件的完整路径"
                        },
                        "sheet_name": {
                            "type": "string",
                            "description": "指定要处理的工作表名称（可选）"
                        },
                        "config": {
                            "type": "object",
                            "description": "分割配置参数（可选）",
                            "properties": {
                                "min_segment_size": {
                                    "type": "integer",
                                    "description": "最小片段大小",
                                    "minimum": 1
                                },
                                "boundary_threshold": {
                                    "type": "number",
                                    "description": "边界检测阈值",
                                    "minimum": 0.0,
                                    "maximum": 1.0
                                },
                                "semantic_weight": {
                                    "type": "number",
                                    "description": "语义特征权重"
                                },
                                "layout_weight": {
                                    "type": "number", 
                                    "description": "布局特征权重"
                                },
                                "format_weight": {
                                    "type": "number",
                                    "description": "格式特征权重"
                                },
                                "datatype_weight": {
                                    "type": "number",
                                    "description": "数据类型特征权重"
                                },
                                "unittype_weight": {
                                    "type": "number",
                                    "description": "单元类型特征权重"
                                }
                            }
                        }
                    },
                    "required": ["file_path"]
                }
            ),
            Tool(
                name="analyze_file_structure",
                description="分析Excel文件的结构信息，不进行实际分割",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "Excel文件的完整路径"
                        }
                    },
                    "required": ["file_path"]
                }
            ),
            Tool(
                name="configure_splitter",
                description="配置分割器参数",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "config": {
                            "type": "object",
                            "description": "配置参数",
                            "properties": {
                                "semantic_model": {
                                    "type": "string",
                                    "description": "语义模型名称"
                                },
                                "min_segment_size": {
                                    "type": "integer",
                                    "description": "最小片段大小"
                                },
                                "boundary_threshold": {
                                    "type": "number",
                                    "description": "边界检测阈值"
                                },
                                "semantic_weight": {"type": "number"},
                                "layout_weight": {"type": "number"},
                                "format_weight": {"type": "number"},
                                "datatype_weight": {"type": "number"},
                                "unittype_weight": {"type": "number"}
                            }
                        }
                    },
                    "required": ["config"]
                }
            ),
        ]
    
    @server.call_tool()
    async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理工具调用"""
        global splitter
        
        try:
            if name == "split_excel_file":
                return await handle_split_excel_file(arguments)
            elif name == "analyze_file_structure":
                return await handle_analyze_file_structure(arguments)
            elif name == "configure_splitter":
                return await handle_configure_splitter(arguments)
            else:
                return [TextContent(
                    type="text",
                    text=f"未知工具: {name}"
                )]
        
        except Exception as e:
            logger.error(f"工具调用失败 {name}: {str(e)}")
            return [TextContent(
                type="text",
                text=f"工具调用失败: {str(e)}"
            )]
    
    async def handle_split_excel_file(arguments: Dict[str, Any]) -> List[TextContent]:
        """处理Excel文件分割"""
        global splitter
        
        file_path = arguments.get("file_path")
        sheet_name = arguments.get("sheet_name")
        config_params = arguments.get("config", {})
        
        # 验证文件路径
        validation_result = validate_file_path(file_path)
        if not validation_result["valid"]:
            error_msg = "文件验证失败:\n" + "\n".join(validation_result["errors"])
            return [TextContent(type="text", text=error_msg)]
        
        # 初始化或更新分割器
        if splitter is None:
            config = SplitterConfig()
            splitter = SemanticSplitter(config)
        
        # 更新配置
        if config_params:
            try:
                splitter.update_config(**config_params)
            except Exception as e:
                return [TextContent(type="text", text=f"配置更新失败: {str(e)}")]
        
        # 执行分割
        try:
            results = splitter.split_excel(file_path, sheet_name)
            
            # 格式化结果
            formatted_results = format_results(results, "summary")
            summary_report = create_summary_report(results)
            
            response_text = f"""# 表格分割完成

{summary_report}

## 详细结果

```json
{json.dumps(formatted_results, ensure_ascii=False, indent=2)}
```
"""
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            logger.error(f"分割失败: {str(e)}")
            return [TextContent(type="text", text=f"分割失败: {str(e)}")]
    
    async def handle_analyze_file_structure(arguments: Dict[str, Any]) -> List[TextContent]:
        """处理文件结构分析"""
        global splitter
        
        file_path = arguments.get("file_path")
        
        # 验证文件路径
        validation_result = validate_file_path(file_path)
        if not validation_result["valid"]:
            error_msg = "文件验证失败:\n" + "\n".join(validation_result["errors"])
            return [TextContent(type="text", text=error_msg)]
        
        # 初始化分割器
        if splitter is None:
            config = SplitterConfig()
            splitter = SemanticSplitter(config)
        
        try:
            analysis = splitter.analyze_file_structure(file_path)
            
            response_text = f"""# 文件结构分析

## 基本信息
- 文件路径: {analysis['file_path']}
- 文件大小: {analysis['file_size'] / 1024 / 1024:.2f} MB
- 工作表数量: {analysis['sheet_count']}

## 工作表详情

"""
            
            for sheet_name, sheet_info in analysis['sheets'].items():
                response_text += f"""### {sheet_name}
- 形状: {sheet_info.get('shape', 'N/A')}
- 数据密度: {sheet_info.get('density', 0):.2%}
- 检测语言: {sheet_info.get('detected_language', 'unknown')}
- 逻辑单元数: {sheet_info.get('logical_units_count', 0)}

"""
                
                if 'unit_types' in sheet_info:
                    response_text += "单元类型分布:\n"
                    for unit_type, count in sheet_info['unit_types'].items():
                        response_text += f"- {unit_type}: {count}\n"
                    response_text += "\n"
            
            response_text += f"""
## 完整分析结果

```json
{json.dumps(analysis, ensure_ascii=False, indent=2)}
```
"""
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            logger.error(f"结构分析失败: {str(e)}")
            return [TextContent(type="text", text=f"结构分析失败: {str(e)}")]
    
    async def handle_configure_splitter(arguments: Dict[str, Any]) -> List[TextContent]:
        """处理分割器配置"""
        global splitter
        
        config_params = arguments.get("config", {})
        
        if not config_params:
            return [TextContent(type="text", text="配置参数不能为空")]
        
        # 初始化分割器
        if splitter is None:
            config = SplitterConfig()
            splitter = SemanticSplitter(config)
        
        try:
            # 更新配置
            splitter.update_config(**config_params)
            
            # 验证配置
            validation_result = validate_config(splitter.config)
            
            response_text = "# 配置更新完成\n\n"
            
            if validation_result["valid"]:
                response_text += "✅ 配置验证通过\n\n"
            else:
                response_text += "❌ 配置验证失败:\n"
                for error in validation_result["errors"]:
                    response_text += f"- {error}\n"
                response_text += "\n"
            
            if validation_result["warnings"]:
                response_text += "⚠️ 警告:\n"
                for warning in validation_result["warnings"]:
                    response_text += f"- {warning}\n"
                response_text += "\n"
            
            # 显示当前配置
            current_config = vars(splitter.config)
            response_text += f"""## 当前配置

```json
{json.dumps(current_config, ensure_ascii=False, indent=2)}
```
"""
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            logger.error(f"配置更新失败: {str(e)}")
            return [TextContent(type="text", text=f"配置更新失败: {str(e)}")]
    
    return server


async def main():
    """主函数"""
    # 设置日志
    setup_logging("INFO")
    
    # 创建服务器
    server = create_server()
    
    # 运行服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="mcp-semantic-splitter",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities={}
                )
            )
        )


if __name__ == "__main__":
    asyncio.run(main())
