"""
MCP Semantic Splitter - 智能Excel表格语义分割器

一个基于模型上下文协议(MCP)的智能Excel表格语义分割服务器。
提供多维度语义分析，支持多工作表处理和语言自适应。
"""

from .core.splitter import SemanticSplitter
from .core.models import (
    SplitResult,
    Segment,
    LogicalUnit,
    FeatureVector,
    SplitterConfig,
)
from .server import create_server

__version__ = "0.1.0"
__author__ = "MCP Semantic Splitter Team"

__all__ = [
    "SemanticSplitter",
    "SplitResult",
    "Segment", 
    "LogicalUnit",
    "FeatureVector",
    "SplitterConfig",
    "create_server",
]
