"""
验证器模块

提供各种输入验证功能。
"""

import os
from typing import Any, Dict, List
from ..core.models import SplitterConfig


def validate_file_path(file_path: str) -> Dict[str, Any]:
    """
    验证文件路径
    
    Args:
        file_path: 文件路径
        
    Returns:
        验证结果字典
    """
    result = {
        'valid': False,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        result['errors'].append(f"文件不存在: {file_path}")
        return result
    
    # 检查是否是文件
    if not os.path.isfile(file_path):
        result['errors'].append(f"路径不是文件: {file_path}")
        return result
    
    # 检查文件扩展名
    ext = os.path.splitext(file_path)[1].lower()
    supported_extensions = ['.xlsx', '.xls', '.xlsm']
    
    if ext not in supported_extensions:
        result['errors'].append(f"不支持的文件格式: {ext}，支持的格式: {supported_extensions}")
        return result
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    max_size = 100 * 1024 * 1024  # 100MB
    
    if file_size > max_size:
        result['warnings'].append(f"文件较大 ({file_size / 1024 / 1024:.1f}MB)，处理可能较慢")
    
    # 检查文件权限
    if not os.access(file_path, os.R_OK):
        result['errors'].append(f"没有文件读取权限: {file_path}")
        return result
    
    # 如果没有错误，则验证通过
    if not result['errors']:
        result['valid'] = True
        result['info'] = {
            'file_size': file_size,
            'extension': ext,
            'absolute_path': os.path.abspath(file_path)
        }
    
    return result


def validate_config(config: SplitterConfig) -> Dict[str, Any]:
    """
    验证分割器配置
    
    Args:
        config: 分割器配置
        
    Returns:
        验证结果字典
    """
    result = {
        'valid': False,
        'errors': [],
        'warnings': []
    }
    
    # 验证权重
    try:
        config.validate_weights()
    except ValueError as e:
        result['errors'].append(str(e))
    
    # 验证最小片段大小
    if config.min_segment_size < 1:
        result['errors'].append("最小片段大小必须大于0")
    elif config.min_segment_size > 100:
        result['warnings'].append("最小片段大小较大，可能导致过度合并")
    
    # 验证边界阈值
    if not (0.0 <= config.boundary_threshold <= 1.0):
        result['errors'].append("边界阈值必须在0.0到1.0之间")
    elif config.boundary_threshold < 0.3:
        result['warnings'].append("边界阈值较低，可能产生过多片段")
    elif config.boundary_threshold > 0.8:
        result['warnings'].append("边界阈值较高，可能产生过少片段")
    
    # 验证设备设置
    if config.device not in ['cpu', 'cuda']:
        result['warnings'].append(f"未知设备类型: {config.device}，将使用CPU")
    
    # 验证输出格式
    if config.output_format not in ['json', 'csv', 'excel']:
        result['warnings'].append(f"未知输出格式: {config.output_format}")
    
    # 如果没有错误，则验证通过
    if not result['errors']:
        result['valid'] = True
    
    return result


def validate_sheet_name(sheet_names: List[str], target_sheet: str) -> bool:
    """
    验证工作表名称是否存在
    
    Args:
        sheet_names: 可用的工作表名称列表
        target_sheet: 目标工作表名称
        
    Returns:
        是否存在
    """
    return target_sheet in sheet_names


def validate_segment_parameters(
    min_size: int,
    max_size: int,
    threshold: float
) -> Dict[str, Any]:
    """
    验证分段参数
    
    Args:
        min_size: 最小片段大小
        max_size: 最大片段大小
        threshold: 阈值
        
    Returns:
        验证结果
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    if min_size <= 0:
        result['errors'].append("最小片段大小必须大于0")
        result['valid'] = False
    
    if max_size <= 0:
        result['errors'].append("最大片段大小必须大于0")
        result['valid'] = False
    
    if min_size >= max_size:
        result['errors'].append("最小片段大小必须小于最大片段大小")
        result['valid'] = False
    
    if not (0.0 <= threshold <= 1.0):
        result['errors'].append("阈值必须在0.0到1.0之间")
        result['valid'] = False
    
    return result
