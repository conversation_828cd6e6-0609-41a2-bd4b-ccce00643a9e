"""
命令行接口

提供命令行工具来使用表格语义分割功能。
"""

import argparse
import json
import sys
import os
from typing import Optional

from .core.splitter import SemanticSplitter
from .core.models import SplitterConfig
from .utils.logging_config import setup_logging
from .utils.validators import validate_file_path
from .utils.formatters import (
    format_results, 
    export_to_json, 
    export_to_csv,
    create_summary_report
)


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="MCP Semantic Splitter - 智能Excel表格语义分割工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 分割Excel文件
  mcp-semantic-splitter split input.xlsx
  
  # 分割特定工作表并导出为JSON
  mcp-semantic-splitter split input.xlsx -s "Sheet1" -o result.json
  
  # 使用自定义配置
  mcp-semantic-splitter split input.xlsx --min-size 10 --threshold 0.8
  
  # 分析文件结构
  mcp-semantic-splitter analyze input.xlsx
  
  # 启动MCP服务器
  mcp-semantic-splitter server
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # Split命令
    split_parser = subparsers.add_parser('split', help='分割Excel文件')
    split_parser.add_argument('file', help='Excel文件路径')
    split_parser.add_argument('-s', '--sheet', help='指定工作表名称')
    split_parser.add_argument('-o', '--output', help='输出文件路径')
    split_parser.add_argument('--format', choices=['json', 'csv'], default='json', help='输出格式')
    split_parser.add_argument('--detail', choices=['minimal', 'summary', 'detailed'], 
                             default='summary', help='输出详细程度')
    
    # 配置参数
    split_parser.add_argument('--min-size', type=int, default=5, help='最小片段大小')
    split_parser.add_argument('--threshold', type=float, default=0.6, help='边界检测阈值')
    split_parser.add_argument('--semantic-weight', type=float, default=0.4, help='语义特征权重')
    split_parser.add_argument('--layout-weight', type=float, default=0.2, help='布局特征权重')
    split_parser.add_argument('--format-weight', type=float, default=0.2, help='格式特征权重')
    split_parser.add_argument('--datatype-weight', type=float, default=0.15, help='数据类型特征权重')
    split_parser.add_argument('--unittype-weight', type=float, default=0.05, help='单元类型特征权重')
    split_parser.add_argument('--model', default='paraphrase-multilingual-mpnet-base-v2', 
                             help='语义模型名称')
    split_parser.add_argument('--device', choices=['cpu', 'cuda'], default='cpu', help='计算设备')
    
    # Analyze命令
    analyze_parser = subparsers.add_parser('analyze', help='分析文件结构')
    analyze_parser.add_argument('file', help='Excel文件路径')
    analyze_parser.add_argument('-o', '--output', help='输出文件路径')
    
    # Server命令
    server_parser = subparsers.add_parser('server', help='启动MCP服务器')
    server_parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                              default='INFO', help='日志级别')
    
    # 全局参数
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='日志级别')
    
    return parser


def handle_split_command(args) -> int:
    """处理split命令"""
    # 验证文件
    validation = validate_file_path(args.file)
    if not validation['valid']:
        print("文件验证失败:")
        for error in validation['errors']:
            print(f"  - {error}")
        return 1
    
    # 显示警告
    for warning in validation['warnings']:
        print(f"警告: {warning}")
    
    try:
        # 创建配置
        config = SplitterConfig(
            min_segment_size=args.min_size,
            boundary_threshold=args.threshold,
            semantic_weight=args.semantic_weight,
            layout_weight=args.layout_weight,
            format_weight=args.format_weight,
            datatype_weight=args.datatype_weight,
            unittype_weight=args.unittype_weight,
            semantic_model=args.model,
            device=args.device
        )
        
        # 验证配置
        config.validate_weights()
        
        # 创建分割器
        print("初始化语义分割器...")
        splitter = SemanticSplitter(config)
        
        # 执行分割
        print(f"正在分割文件: {args.file}")
        if args.sheet:
            print(f"指定工作表: {args.sheet}")
        
        results = splitter.split_excel(args.file, args.sheet)
        
        # 格式化结果
        formatted_results = format_results(results, args.detail)
        
        # 输出结果
        if args.output:
            if args.format == 'json':
                success = export_to_json(results, args.output, args.detail)
            else:  # csv
                success = export_to_csv(results, args.output)
            
            if success:
                print(f"结果已保存到: {args.output}")
            else:
                print("保存结果失败")
                return 1
        else:
            # 输出到控制台
            if args.format == 'json':
                print(json.dumps(formatted_results, ensure_ascii=False, indent=2))
            else:
                # 创建摘要报告
                report = create_summary_report(results)
                print(report)
        
        return 0
        
    except Exception as e:
        print(f"分割失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def handle_analyze_command(args) -> int:
    """处理analyze命令"""
    # 验证文件
    validation = validate_file_path(args.file)
    if not validation['valid']:
        print("文件验证失败:")
        for error in validation['errors']:
            print(f"  - {error}")
        return 1
    
    try:
        # 创建分割器
        print("初始化分析器...")
        splitter = SemanticSplitter()
        
        # 分析文件
        print(f"正在分析文件: {args.file}")
        analysis = splitter.analyze_file_structure(args.file)
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            print(f"分析结果已保存到: {args.output}")
        else:
            # 输出到控制台
            print("\n=== 文件结构分析 ===")
            print(f"文件: {analysis['file_path']}")
            print(f"大小: {analysis['file_size'] / 1024 / 1024:.2f} MB")
            print(f"工作表数量: {analysis['sheet_count']}")
            
            for sheet_name, sheet_info in analysis['sheets'].items():
                print(f"\n工作表: {sheet_name}")
                print(f"  形状: {sheet_info.get('shape', 'N/A')}")
                print(f"  数据密度: {sheet_info.get('density', 0):.2%}")
                print(f"  检测语言: {sheet_info.get('detected_language', 'unknown')}")
                print(f"  逻辑单元数: {sheet_info.get('logical_units_count', 0)}")
                
                if 'unit_types' in sheet_info:
                    print("  单元类型分布:")
                    for unit_type, count in sheet_info['unit_types'].items():
                        print(f"    {unit_type}: {count}")
        
        return 0
        
    except Exception as e:
        print(f"分析失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def handle_server_command(args) -> int:
    """处理server命令"""
    try:
        print("启动MCP服务器...")
        print("使用Ctrl+C停止服务器")
        
        # 设置日志
        setup_logging(args.log_level)
        
        # 启动服务器
        import asyncio
        from .server import main
        asyncio.run(main())
        
        return 0
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return 0
    except Exception as e:
        print(f"服务器启动失败: {str(e)}")
        return 1


def main() -> int:
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, include_timestamp=args.verbose)
    
    # 检查命令
    if not args.command:
        parser.print_help()
        return 1
    
    # 执行命令
    if args.command == 'split':
        return handle_split_command(args)
    elif args.command == 'analyze':
        return handle_analyze_command(args)
    elif args.command == 'server':
        return handle_server_command(args)
    else:
        print(f"未知命令: {args.command}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
