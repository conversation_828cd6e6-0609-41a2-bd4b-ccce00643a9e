"""
数据模型定义

定义了语义分割器使用的所有数据结构和配置类。
"""

from typing import List, Dict, Any, Optional, Union, Tuple
from enum import Enum
from dataclasses import dataclass, field
from pydantic import BaseModel, Field
import numpy as np


class UnitType(Enum):
    """逻辑单元类型枚举"""
    ROW = "row"  # 行单元
    BLOCK = "block"  # 区块单元
    CHART = "chart"  # 图表
    IMAGE = "image"  # 图片
    PIVOT_TABLE = "pivot_table"  # 数据透视表


class SegmentType(Enum):
    """片段类型枚举"""
    DATA_TABLE = "data_table"  # 数据表
    PROSE = "prose"  # 文本段落
    CHART = "chart"  # 图表
    SECTION_TITLE = "section_title"  # 章节标题
    METADATA = "metadata"  # 元数据
    MIXED = "mixed"  # 混合内容


@dataclass
class CellPosition:
    """单元格位置"""
    row: int
    column: int
    
    def __str__(self) -> str:
        return f"R{self.row}C{self.column}"


@dataclass
class CellRange:
    """单元格范围"""
    start: CellPosition
    end: CellPosition
    
    def __str__(self) -> str:
        return f"{self.start}:{self.end}"
    
    @property
    def row_count(self) -> int:
        return self.end.row - self.start.row + 1
    
    @property
    def column_count(self) -> int:
        return self.end.column - self.start.column + 1
    
    @property
    def cell_count(self) -> int:
        return self.row_count * self.column_count


@dataclass
class FeatureVector:
    """多维特征向量"""
    semantic: np.ndarray  # 语义特征向量
    layout: np.ndarray  # 布局特征 [行数, 列数, 单元格数, 合并单元格数, 起始行, 起始列]
    format: np.ndarray  # 格式特征 [字体大小, 是否加粗, 是否斜体, 背景色RGB, 对齐方式]
    datatype: np.ndarray  # 数据类型分布 [文本占比, 数字占比, 日期占比, 其他占比]
    unittype: np.ndarray  # 单元类型独热编码
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "semantic": self.semantic.tolist(),
            "layout": self.layout.tolist(),
            "format": self.format.tolist(),
            "datatype": self.datatype.tolist(),
            "unittype": self.unittype.tolist(),
        }


@dataclass
class LogicalUnit:
    """逻辑单元"""
    unit_id: str
    unit_type: UnitType
    range: CellRange
    content: Any  # 可以是DataFrame、字符串或其他类型
    features: Optional[FeatureVector] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __str__(self) -> str:
        return f"LogicalUnit({self.unit_id}, {self.unit_type.value}, {self.range})"


@dataclass
class Segment:
    """分割片段"""
    segment_id: str
    segment_type: SegmentType
    source_location: str  # 如 "Sheet1!A10:D25"
    units: List[LogicalUnit]
    content: Any  # 合并后的内容
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def total_cells(self) -> int:
        """计算片段包含的总单元格数"""
        return sum(unit.range.cell_count for unit in self.units)
    
    def __str__(self) -> str:
        return f"Segment({self.segment_id}, {self.segment_type.value}, {len(self.units)} units)"


class SplitResult(BaseModel):
    """分割结果"""
    file_path: str
    sheet_name: str
    segments: List[Segment] = Field(default_factory=list)
    processing_time: float = 0.0
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于JSON序列化"""
        return {
            "file_path": self.file_path,
            "sheet_name": self.sheet_name,
            "segments": [
                {
                    "segment_id": seg.segment_id,
                    "segment_type": seg.segment_type.value,
                    "source_location": seg.source_location,
                    "unit_count": len(seg.units),
                    "total_cells": seg.total_cells,
                    "metadata": seg.metadata,
                }
                for seg in self.segments
            ],
            "processing_time": self.processing_time,
            "metadata": self.metadata,
        }


class SplitterConfig(BaseModel):
    """分割器配置"""
    # 语义模型配置
    semantic_model: str = "paraphrase-multilingual-mpnet-base-v2"
    device: str = "cpu"  # 或 "cuda"
    
    # 分割参数
    min_segment_size: int = 5  # 最小片段大小（单元格数）
    boundary_threshold: float = 0.6  # 边界检测阈值
    
    # 特征权重
    semantic_weight: float = 0.4
    layout_weight: float = 0.2
    format_weight: float = 0.2
    datatype_weight: float = 0.15
    unittype_weight: float = 0.05
    
    # 处理选项
    include_hidden: bool = False  # 是否包含隐藏行列
    auto_detect_language: bool = True  # 是否自动检测语言
    merge_small_segments: bool = True  # 是否合并小片段
    
    # 输出选项
    output_format: str = "json"  # 输出格式
    include_content: bool = True  # 是否包含原始内容
    include_features: bool = False  # 是否包含特征向量
    
    def validate_weights(self) -> None:
        """验证权重总和是否为1"""
        total = (
            self.semantic_weight + 
            self.layout_weight + 
            self.format_weight + 
            self.datatype_weight + 
            self.unittype_weight
        )
        if abs(total - 1.0) > 1e-6:
            raise ValueError(f"权重总和必须为1.0，当前为{total}")


@dataclass
class BoundaryScore:
    """边界评分"""
    position: int  # 边界位置（在逻辑单元列表中的索引）
    score: float  # 断裂度分数
    components: Dict[str, float] = field(default_factory=dict)  # 各组件分数
    
    def __str__(self) -> str:
        return f"BoundaryScore(pos={self.position}, score={self.score:.3f})"
