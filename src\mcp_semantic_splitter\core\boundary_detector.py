"""
边界检测模块

通过计算相邻逻辑单元间的"差异度"来找到最佳分割点。
使用局部峰值检测算法识别边界。
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from scipy.signal import find_peaks
from scipy.stats import zscore

from .models import (
    LogicalUnit, 
    BoundaryScore,
    SplitterConfig
)
from .feature_extractor import FeatureExtractor

logger = logging.getLogger(__name__)


class BoundaryDetector:
    """边界检测器"""
    
    def __init__(self, config: SplitterConfig, feature_extractor: FeatureExtractor):
        self.config = config
        self.feature_extractor = feature_extractor
    
    def detect_boundaries(self, units: List[LogicalUnit]) -> List[BoundaryScore]:
        """
        检测逻辑单元列表中的边界点
        
        Args:
            units: 逻辑单元列表
            
        Returns:
            边界评分列表
        """
        if len(units) < 2:
            return []
        
        logger.info(f"开始检测 {len(units)} 个逻辑单元的边界")
        
        # 计算所有相邻单元对的断裂度
        breakdown_scores = self._compute_breakdown_scores(units)
        
        # 使用局部峰值检测找到边界
        boundary_positions = self._detect_local_peaks(breakdown_scores)
        
        # 创建边界评分对象
        boundaries = []
        for pos in boundary_positions:
            if 0 <= pos < len(breakdown_scores):
                score_info = breakdown_scores[pos]
                boundary = BoundaryScore(
                    position=pos,
                    score=score_info['total_score'],
                    components=score_info['components']
                )
                boundaries.append(boundary)
        
        # 按分数排序
        boundaries.sort(key=lambda x: x.score, reverse=True)
        
        logger.info(f"检测到 {len(boundaries)} 个候选边界")
        return boundaries
    
    def _compute_breakdown_scores(self, units: List[LogicalUnit]) -> List[Dict[str, Any]]:
        """
        计算相邻单元对的断裂度分数
        
        Args:
            units: 逻辑单元列表
            
        Returns:
            断裂度分数列表
        """
        breakdown_scores = []
        
        for i in range(len(units) - 1):
            unit1 = units[i]
            unit2 = units[i + 1]
            
            # 计算各维度的差异度
            similarities = self.feature_extractor.compute_feature_similarity(unit1, unit2)
            
            # 转换相似度为差异度（1 - 相似度）
            differences = {
                key: 1.0 - sim for key, sim in similarities.items()
            }
            
            # 计算加权总分
            total_score = (
                differences.get('semantic', 0) * self.config.semantic_weight +
                differences.get('layout', 0) * self.config.layout_weight +
                differences.get('format', 0) * self.config.format_weight +
                differences.get('datatype', 0) * self.config.datatype_weight +
                differences.get('unittype', 0) * self.config.unittype_weight
            )
            
            score_info = {
                'position': i,
                'total_score': total_score,
                'components': differences,
                'unit1_id': unit1.unit_id,
                'unit2_id': unit2.unit_id,
            }
            
            breakdown_scores.append(score_info)
        
        return breakdown_scores
    
    def _detect_local_peaks(self, breakdown_scores: List[Dict[str, Any]]) -> List[int]:
        """
        使用局部峰值检测算法找到边界位置
        
        Args:
            breakdown_scores: 断裂度分数列表
            
        Returns:
            边界位置列表
        """
        if len(breakdown_scores) < 3:
            # 数据点太少，无法进行峰值检测
            return []
        
        # 提取分数序列
        scores = [score_info['total_score'] for score_info in breakdown_scores]
        scores_array = np.array(scores)
        
        # 使用scipy的find_peaks函数
        # 设置最小高度为阈值
        min_height = self.config.boundary_threshold
        
        # 设置最小距离，避免过于密集的边界
        min_distance = max(1, len(scores) // 10)  # 至少间隔10%的长度
        
        # 检测峰值
        peaks, properties = find_peaks(
            scores_array,
            height=min_height,
            distance=min_distance,
            prominence=0.1  # 最小突出度
        )
        
        # 额外的过滤：使用Z-score过滤异常高的峰值
        if len(peaks) > 0:
            peak_scores = scores_array[peaks]
            z_scores = np.abs(zscore(peak_scores))
            
            # 保留Z-score小于2的峰值（不是极端异常值）
            valid_peaks = peaks[z_scores < 2.0]
            
            return valid_peaks.tolist()
        
        return []
    
    def filter_boundaries_by_threshold(self, boundaries: List[BoundaryScore]) -> List[BoundaryScore]:
        """
        根据阈值过滤边界
        
        Args:
            boundaries: 原始边界列表
            
        Returns:
            过滤后的边界列表
        """
        filtered = [
            boundary for boundary in boundaries 
            if boundary.score >= self.config.boundary_threshold
        ]
        
        logger.info(f"阈值过滤：{len(boundaries)} -> {len(filtered)} 个边界")
        return filtered
    
    def adaptive_threshold_selection(self, boundaries: List[BoundaryScore]) -> List[BoundaryScore]:
        """
        自适应阈值选择
        
        Args:
            boundaries: 边界列表
            
        Returns:
            选择后的边界列表
        """
        if not boundaries:
            return []
        
        scores = [b.score for b in boundaries]
        
        # 使用统计方法确定自适应阈值
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        
        # 自适应阈值：均值 + 0.5 * 标准差
        adaptive_threshold = mean_score + 0.5 * std_score
        
        # 但不能低于配置的最小阈值
        final_threshold = max(adaptive_threshold, self.config.boundary_threshold)
        
        filtered = [
            boundary for boundary in boundaries 
            if boundary.score >= final_threshold
        ]
        
        logger.info(f"自适应阈值 {final_threshold:.3f}：{len(boundaries)} -> {len(filtered)} 个边界")
        return filtered
    
    def resolve_boundary_conflicts(self, boundaries: List[BoundaryScore], min_distance: int = 2) -> List[BoundaryScore]:
        """
        解决边界冲突（过于接近的边界）
        
        Args:
            boundaries: 边界列表
            min_distance: 最小距离
            
        Returns:
            解决冲突后的边界列表
        """
        if len(boundaries) <= 1:
            return boundaries
        
        # 按位置排序
        sorted_boundaries = sorted(boundaries, key=lambda x: x.position)
        
        resolved = []
        i = 0
        
        while i < len(sorted_boundaries):
            current = sorted_boundaries[i]
            
            # 查找与当前边界距离过近的边界
            conflicting = [current]
            j = i + 1
            
            while j < len(sorted_boundaries):
                if sorted_boundaries[j].position - current.position < min_distance:
                    conflicting.append(sorted_boundaries[j])
                    j += 1
                else:
                    break
            
            # 在冲突的边界中选择分数最高的
            best_boundary = max(conflicting, key=lambda x: x.score)
            resolved.append(best_boundary)
            
            # 跳过已处理的边界
            i = j
        
        logger.info(f"冲突解决：{len(boundaries)} -> {len(resolved)} 个边界")
        return resolved
    
    def analyze_boundary_quality(self, boundaries: List[BoundaryScore]) -> Dict[str, Any]:
        """
        分析边界质量
        
        Args:
            boundaries: 边界列表
            
        Returns:
            质量分析结果
        """
        if not boundaries:
            return {
                'count': 0,
                'average_score': 0.0,
                'score_std': 0.0,
                'quality': 'no_boundaries'
            }
        
        scores = [b.score for b in boundaries]
        
        analysis = {
            'count': len(boundaries),
            'average_score': np.mean(scores),
            'score_std': np.std(scores),
            'min_score': np.min(scores),
            'max_score': np.max(scores),
            'score_range': np.max(scores) - np.min(scores),
        }
        
        # 质量评估
        if analysis['average_score'] > 0.8:
            quality = 'excellent'
        elif analysis['average_score'] > 0.6:
            quality = 'good'
        elif analysis['average_score'] > 0.4:
            quality = 'fair'
        else:
            quality = 'poor'
        
        analysis['quality'] = quality
        
        # 组件分析
        if boundaries:
            component_scores = {}
            for component in ['semantic', 'layout', 'format', 'datatype', 'unittype']:
                component_values = [
                    b.components.get(component, 0) for b in boundaries
                ]
                if component_values:
                    component_scores[component] = {
                        'mean': np.mean(component_values),
                        'std': np.std(component_values),
                    }
            
            analysis['component_analysis'] = component_scores
        
        return analysis
