"""
测试MCP客户端连接
"""

import asyncio
import subprocess
import sys
import json
import tempfile
import os
import pandas as pd
from mcp.client.session import ClientSession
from mcp.client.stdio import stdio_client


async def create_test_excel():
    """创建测试Excel文件"""
    data = {
        "Name": ["<PERSON>", "<PERSON>", "<PERSON>"],
        "Age": [25, 30, 35],
        "City": ["NYC", "LA", "Chicago"],
    }

    with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_file:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(tmp_file.name, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="TestData", index=False)
        return tmp_file.name


async def test_mcp_connection():
    """测试MCP连接"""
    print("=== 测试MCP服务器连接 ===\n")

    # 创建测试文件
    test_file = await create_test_excel()
    print(f"创建测试文件: {test_file}")

    try:
        # 启动MCP服务器进程
        print("启动MCP服务器...")
        server_process = subprocess.Popen(
            [sys.executable, "-m", "mcp_semantic_splitter.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        print("等待服务器启动...")
        await asyncio.sleep(2)

        # 连接到服务器
        print("连接到MCP服务器...")
        from mcp.client.stdio import StdioServerParameters

        server_params = StdioServerParameters(
            command=sys.executable, args=["-m", "mcp_semantic_splitter.server"]
        )
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("初始化会话...")
                await session.initialize()

                print("✅ MCP服务器连接成功！")

                # 测试列出工具
                print("\n1. 测试列出工具:")
                try:
                    tools = await session.list_tools()
                    print(f"   找到 {len(tools.tools)} 个工具:")
                    for tool in tools.tools:
                        print(f"   - {tool.name}: {tool.description}")
                except Exception as e:
                    print(f"   ❌ 列出工具失败: {str(e)}")

                # 测试列出资源
                print("\n2. 测试列出资源:")
                try:
                    resources = await session.list_resources()
                    print(f"   找到 {len(resources.resources)} 个资源:")
                    for resource in resources.resources:
                        print(f"   - {resource.name}: {resource.description}")
                except Exception as e:
                    print(f"   ❌ 列出资源失败: {str(e)}")

                # 测试配置工具
                print("\n3. 测试配置工具:")
                try:
                    config_result = await session.call_tool(
                        "configure_splitter",
                        {"config": {"min_segment_size": 2, "boundary_threshold": 0.5}},
                    )
                    print("   ✅ 配置工具调用成功")
                    print(f"   响应长度: {len(config_result.content[0].text)} 字符")
                except Exception as e:
                    print(f"   ❌ 配置工具失败: {str(e)}")

                # 测试分析文件结构
                print("\n4. 测试分析文件结构:")
                try:
                    analysis_result = await session.call_tool(
                        "analyze_file_structure", {"file_path": test_file}
                    )
                    print("   ✅ 文件分析成功")
                    print(f"   响应长度: {len(analysis_result.content[0].text)} 字符")
                except Exception as e:
                    print(f"   ❌ 文件分析失败: {str(e)}")

                print("\n🎉 MCP服务器测试完成！")

        # 终止服务器
        server_process.terminate()
        server_process.wait()

    except Exception as e:
        print(f"❌ MCP测试失败: {str(e)}")
        import traceback

        traceback.print_exc()

        # 确保清理进程
        try:
            server_process.terminate()
        except:
            pass

    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"清理测试文件: {test_file}")


async def test_simple_mcp():
    """测试简单的MCP功能"""
    print("=== 测试简单MCP功能 ===\n")

    try:
        # 启动简化的测试服务器
        print("启动简化测试服务器...")
        server_process = subprocess.Popen(
            [sys.executable, "test_mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        await asyncio.sleep(1)

        # 连接测试
        from mcp.client.stdio import StdioServerParameters

        server_params = StdioServerParameters(
            command=sys.executable, args=["test_mcp_server.py"]
        )
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()

                print("✅ 简化MCP服务器连接成功！")

                # 测试工具
                tools = await session.list_tools()
                print(f"找到工具: {[tool.name for tool in tools.tools]}")

                # 调用测试工具
                result = await session.call_tool("test_tool", {"message": "Hello MCP!"})
                print(f"工具响应: {result.content[0].text}")

        server_process.terminate()
        server_process.wait()

        print("✅ 简化MCP测试成功！")

    except Exception as e:
        print(f"❌ 简化MCP测试失败: {str(e)}")
        try:
            server_process.terminate()
        except:
            pass


if __name__ == "__main__":
    asyncio.run(test_simple_mcp())
    print("\n" + "=" * 50 + "\n")
    asyncio.run(test_mcp_connection())
