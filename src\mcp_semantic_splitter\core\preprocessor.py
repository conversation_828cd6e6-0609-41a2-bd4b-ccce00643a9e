"""
预处理模块

负责Excel文件的读取、多工作表处理、语言识别和初步清洗。
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet
from langdetect import detect, LangDetectError
import warnings

from .models import SplitterConfig

# 抑制openpyxl警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

logger = logging.getLogger(__name__)


class ExcelPreprocessor:
    """Excel文件预处理器"""
    
    def __init__(self, config: SplitterConfig):
        self.config = config
        
    def process_file(self, file_path: str) -> Dict[str, pd.DataFrame]:
        """
        处理Excel文件，返回所有工作表的数据
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            字典，键为工作表名，值为DataFrame
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        logger.info(f"开始处理Excel文件: {file_path}")
        
        # 检查文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ['.xlsx', '.xls', '.xlsm']:
            raise ValueError(f"不支持的文件格式: {ext}")
        
        try:
            # 使用openpyxl读取工作簿
            workbook = load_workbook(file_path, data_only=True)
            sheet_data = {}
            
            for sheet_name in workbook.sheetnames:
                logger.info(f"处理工作表: {sheet_name}")
                worksheet = workbook[sheet_name]
                
                # 转换为DataFrame
                df = self._worksheet_to_dataframe(worksheet)
                
                if not df.empty:
                    # 清洗数据
                    df = self._clean_dataframe(df)
                    sheet_data[sheet_name] = df
                    logger.info(f"工作表 {sheet_name} 处理完成，形状: {df.shape}")
                else:
                    logger.warning(f"工作表 {sheet_name} 为空，跳过")
            
            workbook.close()
            logger.info(f"文件处理完成，共处理 {len(sheet_data)} 个工作表")
            return sheet_data
            
        except Exception as e:
            logger.error(f"处理文件时出错: {str(e)}")
            raise
    
    def _worksheet_to_dataframe(self, worksheet: Worksheet) -> pd.DataFrame:
        """
        将openpyxl工作表转换为pandas DataFrame
        
        Args:
            worksheet: openpyxl工作表对象
            
        Returns:
            转换后的DataFrame
        """
        # 获取工作表的实际使用范围
        if worksheet.max_row == 1 and worksheet.max_column == 1:
            # 空工作表
            return pd.DataFrame()
        
        # 读取所有数据
        data = []
        for row in worksheet.iter_rows(values_only=True):
            data.append(list(row))
        
        if not data:
            return pd.DataFrame()
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 处理隐藏行列
        if not self.config.include_hidden:
            df = self._remove_hidden_rows_columns(df, worksheet)
        
        return df
    
    def _remove_hidden_rows_columns(self, df: pd.DataFrame, worksheet: Worksheet) -> pd.DataFrame:
        """
        移除隐藏的行和列
        
        Args:
            df: 原始DataFrame
            worksheet: openpyxl工作表对象
            
        Returns:
            移除隐藏行列后的DataFrame
        """
        # 获取可见行
        visible_rows = []
        for i, row in enumerate(worksheet.iter_rows(), 1):
            if not worksheet.row_dimensions[i].hidden:
                visible_rows.append(i - 1)  # 转换为0索引
        
        # 获取可见列
        visible_cols = []
        for i, col in enumerate(worksheet.iter_cols(), 1):
            col_letter = worksheet.cell(1, i).column_letter
            if not worksheet.column_dimensions[col_letter].hidden:
                visible_cols.append(i - 1)  # 转换为0索引
        
        # 过滤DataFrame
        if visible_rows and visible_cols:
            df = df.iloc[visible_rows, visible_cols]
        
        return df
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗DataFrame数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        # 移除完全空的行和列
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        # 重置索引
        df = df.reset_index(drop=True)
        df.columns = range(len(df.columns))
        
        # 处理单元格内容
        for col in df.columns:
            df[col] = df[col].apply(self._clean_cell_value)
        
        return df
    
    def _clean_cell_value(self, value: Any) -> Any:
        """
        清洗单元格值
        
        Args:
            value: 原始单元格值
            
        Returns:
            清洗后的值
        """
        if pd.isna(value):
            return None
        
        if isinstance(value, str):
            # 去除多余空格
            value = value.strip()
            # 空字符串转为None
            if not value:
                return None
        
        return value
    
    def detect_language(self, df: pd.DataFrame) -> str:
        """
        检测DataFrame中文本的主要语言
        
        Args:
            df: 要检测的DataFrame
            
        Returns:
            检测到的语言代码（如'zh-cn', 'en'等）
        """
        if not self.config.auto_detect_language:
            return 'en'  # 默认英语
        
        # 收集所有文本内容
        text_content = []
        for col in df.columns:
            for value in df[col]:
                if isinstance(value, str) and len(value.strip()) > 0:
                    text_content.append(value.strip())
        
        if not text_content:
            return 'en'  # 没有文本内容，默认英语
        
        # 合并文本进行语言检测
        combined_text = ' '.join(text_content[:100])  # 只取前100个文本片段
        
        try:
            detected_lang = detect(combined_text)
            logger.info(f"检测到的语言: {detected_lang}")
            return detected_lang
        except LangDetectError:
            logger.warning("语言检测失败，使用默认语言: en")
            return 'en'
    
    def get_recommended_model(self, language: str) -> str:
        """
        根据检测到的语言推荐合适的语义模型
        
        Args:
            language: 语言代码
            
        Returns:
            推荐的模型名称
        """
        # 语言到模型的映射
        language_models = {
            'zh': 'paraphrase-multilingual-mpnet-base-v2',  # 中文
            'zh-cn': 'paraphrase-multilingual-mpnet-base-v2',
            'zh-tw': 'paraphrase-multilingual-mpnet-base-v2',
            'en': 'all-MiniLM-L6-v2',  # 英文
            'ja': 'paraphrase-multilingual-mpnet-base-v2',  # 日语
            'ko': 'paraphrase-multilingual-mpnet-base-v2',  # 韩语
            'fr': 'paraphrase-multilingual-mpnet-base-v2',  # 法语
            'de': 'paraphrase-multilingual-mpnet-base-v2',  # 德语
            'es': 'paraphrase-multilingual-mpnet-base-v2',  # 西班牙语
        }
        
        # 获取推荐模型，如果语言不在映射中则使用多语言模型
        recommended = language_models.get(
            language, 
            'paraphrase-multilingual-mpnet-base-v2'
        )
        
        logger.info(f"为语言 {language} 推荐模型: {recommended}")
        return recommended
    
    def analyze_sheet_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析工作表的基本结构信息
        
        Args:
            df: 要分析的DataFrame
            
        Returns:
            结构分析结果
        """
        if df.empty:
            return {
                'is_empty': True,
                'shape': (0, 0),
                'has_data': False,
            }
        
        # 基本信息
        rows, cols = df.shape
        
        # 数据类型分析
        text_cells = 0
        numeric_cells = 0
        date_cells = 0
        empty_cells = 0
        
        for col in df.columns:
            for value in df[col]:
                if pd.isna(value) or value is None:
                    empty_cells += 1
                elif isinstance(value, (int, float)):
                    numeric_cells += 1
                elif isinstance(value, str):
                    text_cells += 1
                else:
                    # 可能是日期或其他类型
                    date_cells += 1
        
        total_cells = rows * cols
        
        return {
            'is_empty': False,
            'shape': (rows, cols),
            'has_data': True,
            'total_cells': total_cells,
            'data_distribution': {
                'text_ratio': text_cells / total_cells if total_cells > 0 else 0,
                'numeric_ratio': numeric_cells / total_cells if total_cells > 0 else 0,
                'date_ratio': date_cells / total_cells if total_cells > 0 else 0,
                'empty_ratio': empty_cells / total_cells if total_cells > 0 else 0,
            },
            'density': (total_cells - empty_cells) / total_cells if total_cells > 0 else 0,
        }
