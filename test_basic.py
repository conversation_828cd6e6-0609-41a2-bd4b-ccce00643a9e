"""
基本功能测试（不需要下载大型模型）
"""

import pandas as pd
from mcp_semantic_splitter.core.models import SplitterConfig
from mcp_semantic_splitter.core.preprocessor import ExcelPreprocessor
from mcp_semantic_splitter.core.parser import StructuralParser

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 1. 测试配置
    print("1. 测试配置创建...")
    config = SplitterConfig()
    print(f"   默认配置创建成功: {config.semantic_model}")
    
    # 2. 测试预处理器
    print("2. 测试预处理器...")
    preprocessor = ExcelPreprocessor(config)
    
    # 创建测试数据
    test_data = {
        'Name': ['Alice', 'Bob', 'Charlie', '', 'Summary'],
        'Age': [25, 30, 35, '', ''],
        'City': ['NYC', 'LA', 'Chicago', '', 'Total: 3 people']
    }
    df = pd.DataFrame(test_data)
    print(f"   测试数据形状: {df.shape}")
    
    # 清洗数据
    cleaned_df = preprocessor._clean_dataframe(df)
    print(f"   清洗后数据形状: {cleaned_df.shape}")
    
    # 语言检测
    language = preprocessor.detect_language(cleaned_df)
    print(f"   检测到的语言: {language}")
    
    # 结构分析
    structure = preprocessor.analyze_sheet_structure(cleaned_df)
    print(f"   数据密度: {structure['density']:.2%}")
    
    # 3. 测试结构解析器
    print("3. 测试结构解析器...")
    parser = StructuralParser(config)
    
    # 解析逻辑单元
    logical_units = parser.parse_dataframe(cleaned_df, "TestSheet")
    print(f"   识别到 {len(logical_units)} 个逻辑单元")
    
    for i, unit in enumerate(logical_units):
        print(f"     单元 {i+1}: {unit.unit_type.value} - {unit.range}")
    
    print("\n✅ 基本功能测试通过！")

def test_models():
    """测试数据模型"""
    print("\n=== 测试数据模型 ===")
    
    from mcp_semantic_splitter.core.models import (
        CellPosition, CellRange, UnitType, SegmentType
    )
    
    # 测试位置和范围
    pos1 = CellPosition(row=1, column=1)
    pos2 = CellPosition(row=3, column=5)
    range_obj = CellRange(start=pos1, end=pos2)
    
    print(f"位置1: {pos1}")
    print(f"位置2: {pos2}")
    print(f"范围: {range_obj}")
    print(f"单元格数: {range_obj.cell_count}")
    
    # 测试枚举
    print(f"单元类型: {[t.value for t in UnitType]}")
    print(f"片段类型: {[t.value for t in SegmentType]}")
    
    print("✅ 数据模型测试通过！")

def test_validators():
    """测试验证器"""
    print("\n=== 测试验证器 ===")
    
    from mcp_semantic_splitter.utils.validators import validate_config
    
    # 测试有效配置
    config = SplitterConfig()
    result = validate_config(config)
    print(f"配置验证结果: {result['valid']}")
    
    if result['errors']:
        print(f"错误: {result['errors']}")
    if result['warnings']:
        print(f"警告: {result['warnings']}")
    
    print("✅ 验证器测试通过！")

def test_formatters():
    """测试格式化器"""
    print("\n=== 测试格式化器 ===")
    
    from mcp_semantic_splitter.utils.formatters import create_summary_report
    from mcp_semantic_splitter.core.models import SplitResult
    
    # 创建测试结果
    result = SplitResult(
        file_path="test.xlsx",
        sheet_name="TestSheet",
        segments=[],
        processing_time=1.5,
        metadata={'status': 'success'}
    )
    
    # 生成报告
    report = create_summary_report([result])
    print("生成的摘要报告:")
    print(report[:200] + "...")
    
    print("✅ 格式化器测试通过！")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_models()
        test_validators()
        test_formatters()
        
        print("\n🎉 所有测试通过！项目基础功能正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
