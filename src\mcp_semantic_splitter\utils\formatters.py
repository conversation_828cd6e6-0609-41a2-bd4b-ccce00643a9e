"""
格式化器模块

提供结果格式化和导出功能。
"""

import json
import csv
import os
from typing import List, Dict, Any, Optional
import pandas as pd

from ..core.models import SplitResult, Segment


def format_results(results: List[SplitResult], format_type: str = "summary") -> Dict[str, Any]:
    """
    格式化分割结果
    
    Args:
        results: 分割结果列表
        format_type: 格式类型 ("summary", "detailed", "minimal")
        
    Returns:
        格式化后的结果字典
    """
    if format_type == "minimal":
        return _format_minimal(results)
    elif format_type == "detailed":
        return _format_detailed(results)
    else:  # summary
        return _format_summary(results)


def _format_summary(results: List[SplitResult]) -> Dict[str, Any]:
    """格式化摘要结果"""
    total_segments = sum(len(result.segments) for result in results)
    total_time = sum(result.processing_time for result in results)
    
    summary = {
        "overview": {
            "total_sheets": len(results),
            "total_segments": total_segments,
            "total_processing_time": round(total_time, 2),
            "average_time_per_sheet": round(total_time / len(results), 2) if results else 0
        },
        "sheets": []
    }
    
    for result in results:
        sheet_info = {
            "sheet_name": result.sheet_name,
            "segments_count": len(result.segments),
            "processing_time": round(result.processing_time, 2),
            "status": result.metadata.get("status", "unknown"),
            "segment_types": {}
        }
        
        # 统计片段类型
        for segment in result.segments:
            seg_type = segment.segment_type.value
            sheet_info["segment_types"][seg_type] = sheet_info["segment_types"].get(seg_type, 0) + 1
        
        summary["sheets"].append(sheet_info)
    
    return summary


def _format_detailed(results: List[SplitResult]) -> Dict[str, Any]:
    """格式化详细结果"""
    detailed = {
        "results": []
    }
    
    for result in results:
        result_dict = result.to_dict()
        
        # 添加详细的片段信息
        detailed_segments = []
        for segment in result.segments:
            segment_info = {
                "segment_id": segment.segment_id,
                "segment_type": segment.segment_type.value,
                "source_location": segment.source_location,
                "unit_count": len(segment.units),
                "total_cells": segment.total_cells,
                "metadata": segment.metadata,
                "units": []
            }
            
            # 添加单元信息
            for unit in segment.units:
                unit_info = {
                    "unit_id": unit.unit_id,
                    "unit_type": unit.unit_type.value,
                    "range": str(unit.range),
                    "metadata": unit.metadata
                }
                segment_info["units"].append(unit_info)
            
            detailed_segments.append(segment_info)
        
        result_dict["detailed_segments"] = detailed_segments
        detailed["results"].append(result_dict)
    
    return detailed


def _format_minimal(results: List[SplitResult]) -> Dict[str, Any]:
    """格式化最小结果"""
    minimal = {
        "total_sheets": len(results),
        "total_segments": sum(len(result.segments) for result in results),
        "sheets": {}
    }
    
    for result in results:
        minimal["sheets"][result.sheet_name] = {
            "segments": [
                {
                    "id": seg.segment_id,
                    "type": seg.segment_type.value,
                    "location": seg.source_location
                }
                for seg in result.segments
            ]
        }
    
    return minimal


def export_to_json(
    results: List[SplitResult], 
    output_path: str,
    format_type: str = "summary",
    indent: int = 2
) -> bool:
    """
    导出结果到JSON文件
    
    Args:
        results: 分割结果列表
        output_path: 输出文件路径
        format_type: 格式类型
        indent: JSON缩进
        
    Returns:
        是否成功
    """
    try:
        formatted_results = format_results(results, format_type)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(formatted_results, f, ensure_ascii=False, indent=indent)
        
        return True
    except Exception as e:
        print(f"导出JSON失败: {str(e)}")
        return False


def export_to_csv(results: List[SplitResult], output_path: str) -> bool:
    """
    导出结果到CSV文件
    
    Args:
        results: 分割结果列表
        output_path: 输出文件路径
        
    Returns:
        是否成功
    """
    try:
        rows = []
        
        for result in results:
            for segment in result.segments:
                row = {
                    "file_path": result.file_path,
                    "sheet_name": result.sheet_name,
                    "segment_id": segment.segment_id,
                    "segment_type": segment.segment_type.value,
                    "source_location": segment.source_location,
                    "unit_count": len(segment.units),
                    "total_cells": segment.total_cells,
                    "processing_time": result.processing_time
                }
                rows.append(row)
        
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        return True
    except Exception as e:
        print(f"导出CSV失败: {str(e)}")
        return False


def export_segments_content(
    results: List[SplitResult], 
    output_dir: str,
    include_content: bool = True
) -> bool:
    """
    导出片段内容到单独的文件
    
    Args:
        results: 分割结果列表
        output_dir: 输出目录
        include_content: 是否包含内容
        
    Returns:
        是否成功
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        for result in results:
            sheet_dir = os.path.join(output_dir, result.sheet_name)
            os.makedirs(sheet_dir, exist_ok=True)
            
            for segment in result.segments:
                segment_file = os.path.join(sheet_dir, f"{segment.segment_id}.json")
                
                segment_data = {
                    "segment_id": segment.segment_id,
                    "segment_type": segment.segment_type.value,
                    "source_location": segment.source_location,
                    "metadata": segment.metadata,
                    "units": [
                        {
                            "unit_id": unit.unit_id,
                            "unit_type": unit.unit_type.value,
                            "range": str(unit.range),
                            "content": unit.content if include_content else None,
                            "metadata": unit.metadata
                        }
                        for unit in segment.units
                    ]
                }
                
                if include_content and segment.content is not None:
                    if isinstance(segment.content, pd.DataFrame):
                        segment_data["content"] = segment.content.to_dict('records')
                    else:
                        segment_data["content"] = segment.content
                
                with open(segment_file, 'w', encoding='utf-8') as f:
                    json.dump(segment_data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"导出片段内容失败: {str(e)}")
        return False


def create_summary_report(results: List[SplitResult]) -> str:
    """
    创建摘要报告
    
    Args:
        results: 分割结果列表
        
    Returns:
        摘要报告文本
    """
    if not results:
        return "没有分割结果。"
    
    total_segments = sum(len(result.segments) for result in results)
    total_time = sum(result.processing_time for result in results)
    
    report = f"""
# 表格语义分割报告

## 总体统计
- 处理工作表数量: {len(results)}
- 生成片段总数: {total_segments}
- 总处理时间: {total_time:.2f}秒
- 平均每表处理时间: {total_time / len(results):.2f}秒

## 各工作表详情
"""
    
    for result in results:
        report += f"\n### {result.sheet_name}\n"
        report += f"- 片段数量: {len(result.segments)}\n"
        report += f"- 处理时间: {result.processing_time:.2f}秒\n"
        report += f"- 状态: {result.metadata.get('status', '未知')}\n"
        
        # 片段类型统计
        type_counts = {}
        for segment in result.segments:
            seg_type = segment.segment_type.value
            type_counts[seg_type] = type_counts.get(seg_type, 0) + 1
        
        if type_counts:
            report += "- 片段类型分布:\n"
            for seg_type, count in type_counts.items():
                report += f"  - {seg_type}: {count}\n"
    
    return report
