"""
特征提取模块

为每个逻辑单元提取多维特征向量，包括语义、布局、格式、数据类型和单元类型特征。
"""

import logging
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import StandardScaler
import warnings

from .models import (
    LogicalUnit, 
    FeatureVector, 
    UnitType,
    SplitterConfig
)

# 抑制警告
warnings.filterwarnings('ignore', category=FutureWarning)

logger = logging.getLogger(__name__)


class FeatureExtractor:
    """特征提取器"""
    
    def __init__(self, config: SplitterConfig):
        self.config = config
        self.semantic_model = None
        self.scaler = StandardScaler()
        self._load_semantic_model()
    
    def _load_semantic_model(self):
        """加载语义模型"""
        try:
            logger.info(f"加载语义模型: {self.config.semantic_model}")
            self.semantic_model = SentenceTransformer(
                self.config.semantic_model,
                device=self.config.device
            )
            logger.info("语义模型加载成功")
        except Exception as e:
            logger.error(f"加载语义模型失败: {str(e)}")
            raise
    
    def extract_features(self, units: List[LogicalUnit]) -> List[LogicalUnit]:
        """
        为逻辑单元列表提取特征
        
        Args:
            units: 逻辑单元列表
            
        Returns:
            包含特征向量的逻辑单元列表
        """
        if not units:
            return units
        
        logger.info(f"开始为 {len(units)} 个逻辑单元提取特征")
        
        # 批量提取语义特征
        semantic_features = self._extract_semantic_features_batch(units)
        
        # 为每个单元提取其他特征
        for i, unit in enumerate(units):
            # 提取各类特征
            layout_features = self._extract_layout_features(unit)
            format_features = self._extract_format_features(unit)
            datatype_features = self._extract_datatype_features(unit)
            unittype_features = self._extract_unittype_features(unit)
            
            # 组合特征向量
            feature_vector = FeatureVector(
                semantic=semantic_features[i],
                layout=layout_features,
                format=format_features,
                datatype=datatype_features,
                unittype=unittype_features
            )
            
            unit.features = feature_vector
        
        logger.info("特征提取完成")
        return units
    
    def _extract_semantic_features_batch(self, units: List[LogicalUnit]) -> List[np.ndarray]:
        """
        批量提取语义特征
        
        Args:
            units: 逻辑单元列表
            
        Returns:
            语义特征向量列表
        """
        # 准备文本内容
        texts = []
        for unit in units:
            text = self._unit_to_text(unit)
            texts.append(text)
        
        # 批量编码
        try:
            embeddings = self.semantic_model.encode(
                texts,
                batch_size=32,
                show_progress_bar=False,
                convert_to_numpy=True
            )
            return [emb for emb in embeddings]
        except Exception as e:
            logger.error(f"语义特征提取失败: {str(e)}")
            # 返回零向量作为fallback
            dim = 384  # 默认维度
            return [np.zeros(dim) for _ in units]
    
    def _unit_to_text(self, unit: LogicalUnit) -> str:
        """
        将逻辑单元转换为文本
        
        Args:
            unit: 逻辑单元
            
        Returns:
            文本表示
        """
        if unit.unit_type == UnitType.ROW:
            # 行单元：连接所有非空值
            if isinstance(unit.content, dict):
                values = [str(v) for v in unit.content.values() if pd.notna(v) and v != '']
                return ' '.join(values)
        elif unit.unit_type == UnitType.BLOCK:
            # 区块单元：直接返回内容
            if isinstance(unit.content, str):
                return unit.content
        elif unit.unit_type in [UnitType.CHART, UnitType.IMAGE]:
            # 特殊单元：返回类型和内容
            return f"{unit.unit_type.value}: {str(unit.content)}"
        
        # 默认情况
        return str(unit.content) if unit.content else ""
    
    def _extract_layout_features(self, unit: LogicalUnit) -> np.ndarray:
        """
        提取布局特征
        
        Args:
            unit: 逻辑单元
            
        Returns:
            布局特征向量 [行数, 列数, 单元格数, 合并单元格数, 起始行, 起始列]
        """
        cell_range = unit.range
        
        features = np.array([
            cell_range.row_count,  # 行数
            cell_range.column_count,  # 列数
            cell_range.cell_count,  # 单元格数
            0,  # 合并单元格数（简化实现，设为0）
            cell_range.start.row,  # 起始行
            cell_range.start.column,  # 起始列
        ], dtype=np.float32)
        
        return features
    
    def _extract_format_features(self, unit: LogicalUnit) -> np.ndarray:
        """
        提取格式特征
        
        Args:
            unit: 逻辑单元
            
        Returns:
            格式特征向量 [字体大小, 是否加粗, 是否斜体, 背景色R, 背景色G, 背景色B, 对齐方式]
        """
        # 简化实现：返回默认格式特征
        # 实际应用中需要从Excel文件中读取格式信息
        features = np.array([
            12.0,  # 字体大小（默认12）
            0.0,   # 是否加粗（0=否，1=是）
            0.0,   # 是否斜体（0=否，1=是）
            255.0, # 背景色R（默认白色）
            255.0, # 背景色G
            255.0, # 背景色B
            0.0,   # 对齐方式（0=左对齐，1=居中，2=右对齐）
        ], dtype=np.float32)
        
        return features
    
    def _extract_datatype_features(self, unit: LogicalUnit) -> np.ndarray:
        """
        提取数据类型特征
        
        Args:
            unit: 逻辑单元
            
        Returns:
            数据类型分布向量 [文本占比, 数字占比, 日期占比, 其他占比]
        """
        text_count = 0
        numeric_count = 0
        date_count = 0
        other_count = 0
        total_count = 0
        
        if unit.unit_type == UnitType.ROW and isinstance(unit.content, dict):
            # 行单元：分析每个单元格的数据类型
            for value in unit.content.values():
                if pd.notna(value) and value != '':
                    total_count += 1
                    if isinstance(value, (int, float)):
                        numeric_count += 1
                    elif isinstance(value, str):
                        text_count += 1
                    else:
                        # 可能是日期或其他类型
                        try:
                            pd.to_datetime(value)
                            date_count += 1
                        except:
                            other_count += 1
        elif unit.unit_type == UnitType.BLOCK:
            # 区块单元：主要是文本
            if isinstance(unit.content, str) and unit.content.strip():
                total_count = 1
                text_count = 1
        else:
            # 特殊单元
            total_count = 1
            other_count = 1
        
        # 计算比例
        if total_count > 0:
            features = np.array([
                text_count / total_count,
                numeric_count / total_count,
                date_count / total_count,
                other_count / total_count,
            ], dtype=np.float32)
        else:
            features = np.array([0.0, 0.0, 0.0, 0.0], dtype=np.float32)
        
        return features
    
    def _extract_unittype_features(self, unit: LogicalUnit) -> np.ndarray:
        """
        提取单元类型特征（独热编码）
        
        Args:
            unit: 逻辑单元
            
        Returns:
            单元类型独热编码向量
        """
        # 定义类型顺序
        unit_types = [UnitType.ROW, UnitType.BLOCK, UnitType.CHART, UnitType.IMAGE, UnitType.PIVOT_TABLE]
        
        # 创建独热编码
        features = np.zeros(len(unit_types), dtype=np.float32)
        try:
            type_index = unit_types.index(unit.unit_type)
            features[type_index] = 1.0
        except ValueError:
            # 未知类型，保持全零
            pass
        
        return features
    
    def normalize_features(self, units: List[LogicalUnit]) -> List[LogicalUnit]:
        """
        标准化特征向量
        
        Args:
            units: 包含特征的逻辑单元列表
            
        Returns:
            标准化后的逻辑单元列表
        """
        if not units or not units[0].features:
            return units
        
        # 收集所有布局和格式特征进行标准化
        layout_features = []
        format_features = []
        
        for unit in units:
            if unit.features:
                layout_features.append(unit.features.layout)
                format_features.append(unit.features.format)
        
        if layout_features:
            # 标准化布局特征
            layout_array = np.array(layout_features)
            layout_normalized = self.scaler.fit_transform(layout_array)
            
            # 标准化格式特征
            format_array = np.array(format_features)
            format_normalized = self.scaler.fit_transform(format_array)
            
            # 更新特征向量
            for i, unit in enumerate(units):
                if unit.features:
                    unit.features.layout = layout_normalized[i]
                    unit.features.format = format_normalized[i]
        
        return units
    
    def compute_feature_similarity(self, unit1: LogicalUnit, unit2: LogicalUnit) -> Dict[str, float]:
        """
        计算两个逻辑单元之间的特征相似度
        
        Args:
            unit1: 第一个逻辑单元
            unit2: 第二个逻辑单元
            
        Returns:
            各类特征的相似度字典
        """
        if not (unit1.features and unit2.features):
            return {}
        
        similarities = {}
        
        # 语义相似度（余弦相似度）
        semantic_sim = np.dot(unit1.features.semantic, unit2.features.semantic) / (
            np.linalg.norm(unit1.features.semantic) * np.linalg.norm(unit2.features.semantic) + 1e-8
        )
        similarities['semantic'] = float(semantic_sim)
        
        # 布局相似度（欧氏距离的倒数）
        layout_dist = np.linalg.norm(unit1.features.layout - unit2.features.layout)
        similarities['layout'] = 1.0 / (1.0 + layout_dist)
        
        # 格式相似度
        format_dist = np.linalg.norm(unit1.features.format - unit2.features.format)
        similarities['format'] = 1.0 / (1.0 + format_dist)
        
        # 数据类型相似度（JS散度的倒数）
        datatype_sim = 1.0 - self._js_divergence(unit1.features.datatype, unit2.features.datatype)
        similarities['datatype'] = max(0.0, datatype_sim)
        
        # 单元类型相似度（点积）
        unittype_sim = np.dot(unit1.features.unittype, unit2.features.unittype)
        similarities['unittype'] = float(unittype_sim)
        
        return similarities
    
    def _js_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """
        计算两个概率分布的JS散度
        
        Args:
            p: 第一个分布
            q: 第二个分布
            
        Returns:
            JS散度值
        """
        # 确保是概率分布
        p = p + 1e-8
        q = q + 1e-8
        p = p / np.sum(p)
        q = q / np.sum(q)
        
        # 计算平均分布
        m = 0.5 * (p + q)
        
        # 计算KL散度
        kl_pm = np.sum(p * np.log(p / m))
        kl_qm = np.sum(q * np.log(q / m))
        
        # JS散度
        js_div = 0.5 * kl_pm + 0.5 * kl_qm
        
        return float(js_div)
