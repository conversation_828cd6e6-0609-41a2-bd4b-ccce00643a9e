"""
测试主要分割器功能
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
from unittest.mock import Mock, patch

from mcp_semantic_splitter.core.splitter import SemanticSplitter
from mcp_semantic_splitter.core.models import SplitterConfig, UnitType, SegmentType


class TestSemanticSplitter:
    """测试语义分割器"""
    
    def setup_method(self):
        """设置测试方法"""
        self.config = SplitterConfig(
            semantic_model="all-MiniLM-L6-v2",  # 使用较小的模型进行测试
            device="cpu",
            min_segment_size=2,
            boundary_threshold=0.5
        )
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_splitter_initialization(self, mock_transformer):
        """测试分割器初始化"""
        mock_transformer.return_value = Mock()
        
        splitter = SemanticSplitter(self.config)
        assert splitter.config == self.config
        assert splitter.preprocessor is not None
        assert splitter.parser is not None
        assert splitter.feature_extractor is not None
        assert splitter.boundary_detector is not None
        assert splitter.segment_generator is not None
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_split_dataframe_simple(self, mock_transformer):
        """测试简单DataFrame分割"""
        # Mock语义模型
        mock_model = Mock()
        mock_model.encode.return_value = np.random.rand(3, 384)  # 3行数据，384维向量
        mock_transformer.return_value = mock_model
        
        # 创建测试数据
        df = pd.DataFrame({
            0: ["Name", "John", "Jane"],
            1: ["Age", "25", "30"],
            2: ["City", "NYC", "LA"]
        })
        
        splitter = SemanticSplitter(self.config)
        result = splitter.split_dataframe(df, "TestSheet")
        
        assert result.sheet_name == "TestSheet"
        assert len(result.segments) > 0
        assert result.processing_time > 0
        assert result.metadata["status"] == "success"
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_split_dataframe_empty(self, mock_transformer):
        """测试空DataFrame分割"""
        mock_transformer.return_value = Mock()
        
        df = pd.DataFrame()
        splitter = SemanticSplitter(self.config)
        result = splitter.split_dataframe(df, "EmptySheet")
        
        assert result.sheet_name == "EmptySheet"
        assert len(result.segments) == 0
        assert result.metadata["status"] == "no_units"
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_config_update(self, mock_transformer):
        """测试配置更新"""
        mock_transformer.return_value = Mock()
        
        splitter = SemanticSplitter(self.config)
        original_threshold = splitter.config.boundary_threshold
        
        # 更新配置
        splitter.update_config(boundary_threshold=0.8, min_segment_size=10)
        
        assert splitter.config.boundary_threshold == 0.8
        assert splitter.config.min_segment_size == 10
        assert splitter.config.boundary_threshold != original_threshold
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_analyze_file_structure_mock(self, mock_transformer):
        """测试文件结构分析（模拟）"""
        mock_transformer.return_value = Mock()
        
        splitter = SemanticSplitter(self.config)
        
        # 由于我们没有真实的Excel文件，这里只测试方法存在
        assert hasattr(splitter, 'analyze_file_structure')
        assert callable(splitter.analyze_file_structure)


class TestSplitterIntegration:
    """集成测试"""
    
    def create_test_excel_file(self):
        """创建测试Excel文件"""
        # 创建临时Excel文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            # 创建测试数据
            data = {
                'Name': ['Alice', 'Bob', 'Charlie', '', 'Summary'],
                'Age': [25, 30, 35, '', ''],
                'City': ['NYC', 'LA', 'Chicago', '', 'Total: 3 people'],
                'Salary': [50000, 60000, 70000, '', '']
            }
            df = pd.DataFrame(data)
            
            # 写入Excel文件
            with pd.ExcelWriter(tmp_file.name, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='TestData', index=False)
            
            return tmp_file.name
    
    @patch('mcp_semantic_splitter.core.feature_extractor.SentenceTransformer')
    def test_full_pipeline_with_excel(self, mock_transformer):
        """测试完整的Excel处理流程"""
        # Mock语义模型
        mock_model = Mock()
        mock_model.encode.return_value = np.random.rand(5, 384)  # 5行数据
        mock_transformer.return_value = mock_model
        
        # 创建测试文件
        test_file = self.create_test_excel_file()
        
        try:
            config = SplitterConfig(
                semantic_model="all-MiniLM-L6-v2",
                device="cpu",
                min_segment_size=1,
                boundary_threshold=0.3
            )
            
            splitter = SemanticSplitter(config)
            results = splitter.split_excel(test_file)
            
            assert len(results) > 0
            result = results[0]
            assert result.sheet_name == "TestData"
            assert result.metadata["status"] == "success"
            
            # 验证分析功能
            analysis = splitter.analyze_file_structure(test_file)
            assert "TestData" in analysis["sheets"]
            assert analysis["sheet_count"] == 1
            
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.unlink(test_file)


class TestConfigValidation:
    """测试配置验证"""
    
    def test_valid_config(self):
        """测试有效配置"""
        config = SplitterConfig()
        config.validate_weights()  # 不应该抛出异常
    
    def test_invalid_weights(self):
        """测试无效权重"""
        config = SplitterConfig(
            semantic_weight=0.5,
            layout_weight=0.3,
            format_weight=0.3,  # 总和 > 1
            datatype_weight=0.1,
            unittype_weight=0.1
        )
        
        with pytest.raises(ValueError, match="权重总和必须为1.0"):
            config.validate_weights()
    
    def test_boundary_conditions(self):
        """测试边界条件"""
        # 测试最小值
        config = SplitterConfig(min_segment_size=1, boundary_threshold=0.0)
        assert config.min_segment_size == 1
        assert config.boundary_threshold == 0.0
        
        # 测试最大值
        config = SplitterConfig(boundary_threshold=1.0)
        assert config.boundary_threshold == 1.0


if __name__ == "__main__":
    pytest.main([__file__])
