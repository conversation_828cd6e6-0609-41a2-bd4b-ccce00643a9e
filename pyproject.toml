[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcp-semantic-splitter"
version = "0.1.0"
description = "A Model Context Protocol (MCP) server for intelligent semantic splitting of Excel tables"
authors = [
    {name = "MCP Semantic Splitter Team"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["mcp", "excel", "semantic", "splitter", "table", "ai"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "mcp>=1.0.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "xlrd>=2.0.0",
    "sentence-transformers>=2.2.0",
    "scikit-learn>=1.3.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "langdetect>=1.0.9",
    "Pillow>=10.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/mcp-semantic-splitter"
Repository = "https://github.com/your-org/mcp-semantic-splitter"
Issues = "https://github.com/your-org/mcp-semantic-splitter/issues"

[project.scripts]
mcp-semantic-splitter = "mcp_semantic_splitter.server:main"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_semantic_splitter"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
    "/LICENSE",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mcp_semantic_splitter"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "sentence_transformers.*",
    "langdetect.*",
    "openpyxl.*",
    "xlrd.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/mcp_semantic_splitter",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["src/mcp_semantic_splitter"]
omit = [
    "*/tests/*",
    "*/test_*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
