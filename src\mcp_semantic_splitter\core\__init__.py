"""
核心模块

包含表格语义分割的所有核心功能。
"""

from .splitter import SemanticSplitter
from .models import (
    SplitterConfig,
    SplitResult,
    Segment,
    LogicalUnit,
    FeatureVector,
    UnitType,
    SegmentType,
    BoundaryScore,
)
from .preprocessor import ExcelPreprocessor
from .parser import StructuralParser
from .feature_extractor import FeatureExtractor
from .boundary_detector import BoundaryDetector
from .segment_generator import SegmentGenerator

__all__ = [
    "SemanticSplitter",
    "SplitterConfig",
    "SplitResult",
    "Segment",
    "LogicalUnit",
    "FeatureVector",
    "UnitType",
    "SegmentType",
    "BoundaryScore",
    "ExcelPreprocessor",
    "StructuralParser",
    "FeatureExtractor",
    "BoundaryDetector",
    "SegmentGenerator",
]
