"""
模型下载脚本
在有网络连接时预下载所需的语义模型
"""

import os
import logging
from sentence_transformers import SentenceTransformer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_mirror():
    """设置Hugging Face镜像源"""
    # 设置环境变量使用镜像源
    mirror_urls = [
        "https://hf-mirror.com",  # 国内镜像
        "https://huggingface.co",  # 官方源作为备选
    ]

    # 优先使用镜像源
    os.environ["HF_ENDPOINT"] = mirror_urls[0]
    logger.info(f"设置镜像源: {mirror_urls[0]}")

    return mirror_urls


def download_models():
    """下载常用的语义模型"""
    models = [
        "all-MiniLM-L6-v2",  # 英文模型
        "paraphrase-multilingual-mpnet-base-v2",  # 多语言模型
    ]

    cache_dir = os.path.join(os.path.expanduser("~"), ".cache", "sentence_transformers")

    # 设置镜像源
    mirror_urls = setup_mirror()

    for model_name in models:
        success = False
        for mirror_url in mirror_urls:
            try:
                # 设置当前镜像
                os.environ["HF_ENDPOINT"] = mirror_url
                logger.info(f"正在从 {mirror_url} 下载模型: {model_name}")

                model = SentenceTransformer(model_name, cache_folder=cache_dir)
                logger.info(f"模型 {model_name} 下载完成")
                success = True
                break

            except Exception as e:
                logger.warning(f"从 {mirror_url} 下载模型 {model_name} 失败: {str(e)}")
                continue

        if not success:
            logger.error(f"所有镜像源都无法下载模型: {model_name}")


def test_model_loading():
    """测试模型加载"""
    try:
        logger.info("测试模型加载...")
        model = SentenceTransformer("all-MiniLM-L6-v2")

        # 简单测试
        test_sentences = ["Hello world", "你好世界"]
        embeddings = model.encode(test_sentences)
        logger.info(f"模型测试成功，生成了 {embeddings.shape} 的嵌入向量")

    except Exception as e:
        logger.error(f"模型测试失败: {str(e)}")


if __name__ == "__main__":
    download_models()
    test_model_loading()
