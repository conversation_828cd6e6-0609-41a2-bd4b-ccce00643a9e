"""
主要的语义分割器类

整合所有模块，提供完整的表格语义分割功能。
"""

import logging
import time
from typing import List, Dict, Any, Optional
import os

from .models import SplitterConfig, SplitResult
from .preprocessor import ExcelPreprocessor
from .parser import StructuralParser
from .feature_extractor import FeatureExtractor
from .boundary_detector import BoundaryDetector
from .segment_generator import SegmentGenerator

logger = logging.getLogger(__name__)


class SemanticSplitter:
    """语义分割器主类"""
    
    def __init__(self, config: Optional[SplitterConfig] = None):
        """
        初始化语义分割器
        
        Args:
            config: 分割器配置，如果为None则使用默认配置
        """
        self.config = config or SplitterConfig()
        self.config.validate_weights()
        
        # 初始化各个模块
        self.preprocessor = ExcelPreprocessor(self.config)
        self.parser = StructuralParser(self.config)
        self.feature_extractor = FeatureExtractor(self.config)
        self.boundary_detector = BoundaryDetector(self.config, self.feature_extractor)
        self.segment_generator = SegmentGenerator(self.config, self.feature_extractor)
        
        logger.info("语义分割器初始化完成")
    
    def split_excel(self, file_path: str, sheet_name: Optional[str] = None) -> List[SplitResult]:
        """
        分割Excel文件
        
        Args:
            file_path: Excel文件路径
            sheet_name: 指定工作表名称，如果为None则处理所有工作表
            
        Returns:
            分割结果列表
        """
        start_time = time.time()
        
        logger.info(f"开始分割Excel文件: {file_path}")
        
        # 阶段1：预处理与环境配置
        sheet_data = self.preprocessor.process_file(file_path)
        
        if not sheet_data:
            logger.warning("没有找到有效的工作表数据")
            return []
        
        # 过滤指定的工作表
        if sheet_name:
            if sheet_name in sheet_data:
                sheet_data = {sheet_name: sheet_data[sheet_name]}
            else:
                raise ValueError(f"工作表 '{sheet_name}' 不存在")
        
        results = []
        
        for current_sheet_name, df in sheet_data.items():
            logger.info(f"处理工作表: {current_sheet_name}")
            
            try:
                # 语言检测和模型选择
                detected_language = self.preprocessor.detect_language(df)
                recommended_model = self.preprocessor.get_recommended_model(detected_language)
                
                # 如果推荐的模型与当前模型不同，更新配置
                if recommended_model != self.config.semantic_model:
                    logger.info(f"切换语义模型: {self.config.semantic_model} -> {recommended_model}")
                    self.config.semantic_model = recommended_model
                    # 重新初始化特征提取器
                    self.feature_extractor = FeatureExtractor(self.config)
                    self.boundary_detector = BoundaryDetector(self.config, self.feature_extractor)
                    self.segment_generator = SegmentGenerator(self.config, self.feature_extractor)
                
                # 处理单个工作表
                result = self._split_sheet(file_path, current_sheet_name, df)
                results.append(result)
                
            except Exception as e:
                logger.error(f"处理工作表 {current_sheet_name} 时出错: {str(e)}")
                # 创建错误结果
                error_result = SplitResult(
                    file_path=file_path,
                    sheet_name=current_sheet_name,
                    segments=[],
                    processing_time=0.0,
                    metadata={
                        'error': str(e),
                        'status': 'failed'
                    }
                )
                results.append(error_result)
        
        total_time = time.time() - start_time
        logger.info(f"Excel文件分割完成，总耗时: {total_time:.2f}秒")
        
        return results
    
    def _split_sheet(self, file_path: str, sheet_name: str, df) -> SplitResult:
        """
        分割单个工作表
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称
            df: 工作表数据
            
        Returns:
            分割结果
        """
        start_time = time.time()
        
        # 阶段2：结构化解析与逻辑单元定义
        logical_units = self.parser.parse_dataframe(df, sheet_name)
        
        if not logical_units:
            logger.warning(f"工作表 {sheet_name} 没有识别到逻辑单元")
            return SplitResult(
                file_path=file_path,
                sheet_name=sheet_name,
                segments=[],
                processing_time=time.time() - start_time,
                metadata={'status': 'no_units'}
            )
        
        # 阶段3：多维特征提取
        logical_units = self.feature_extractor.extract_features(logical_units)
        logical_units = self.feature_extractor.normalize_features(logical_units)
        
        # 阶段4：智能边界评分与探测
        boundaries = self.boundary_detector.detect_boundaries(logical_units)
        
        # 边界后处理
        boundaries = self.boundary_detector.filter_boundaries_by_threshold(boundaries)
        boundaries = self.boundary_detector.adaptive_threshold_selection(boundaries)
        boundaries = self.boundary_detector.resolve_boundary_conflicts(boundaries)
        
        # 边界质量分析
        boundary_analysis = self.boundary_detector.analyze_boundary_quality(boundaries)
        
        # 阶段5：分段生成与后处理
        segments = self.segment_generator.generate_segments(logical_units, boundaries, sheet_name)
        
        processing_time = time.time() - start_time
        
        # 创建结果
        result = SplitResult(
            file_path=file_path,
            sheet_name=sheet_name,
            segments=segments,
            processing_time=processing_time,
            metadata={
                'status': 'success',
                'logical_units_count': len(logical_units),
                'boundaries_count': len(boundaries),
                'segments_count': len(segments),
                'boundary_analysis': boundary_analysis,
                'config': self.config.dict() if hasattr(self.config, 'dict') else vars(self.config),
            }
        )
        
        logger.info(f"工作表 {sheet_name} 分割完成: {len(logical_units)} 单元 -> {len(segments)} 片段")
        
        return result
    
    def split_dataframe(self, df, sheet_name: str = "DataFrame") -> SplitResult:
        """
        直接分割DataFrame
        
        Args:
            df: pandas DataFrame
            sheet_name: 虚拟工作表名称
            
        Returns:
            分割结果
        """
        logger.info(f"开始分割DataFrame，形状: {df.shape}")
        
        # 清洗DataFrame
        df = self.preprocessor._clean_dataframe(df)
        
        # 分割
        result = self._split_sheet("DataFrame", sheet_name, df)
        
        return result
    
    def get_config(self) -> SplitterConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, **kwargs) -> None:
        """
        更新配置
        
        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                logger.warning(f"未知配置参数: {key}")
        
        # 验证权重
        self.config.validate_weights()
        
        # 如果语义模型发生变化，重新初始化相关模块
        if 'semantic_model' in kwargs:
            self.feature_extractor = FeatureExtractor(self.config)
            self.boundary_detector = BoundaryDetector(self.config, self.feature_extractor)
            self.segment_generator = SegmentGenerator(self.config, self.feature_extractor)
        
        logger.info("配置更新完成")
    
    def analyze_file_structure(self, file_path: str) -> Dict[str, Any]:
        """
        分析文件结构（不进行分割）
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            结构分析结果
        """
        logger.info(f"分析文件结构: {file_path}")
        
        # 读取文件
        sheet_data = self.preprocessor.process_file(file_path)
        
        analysis = {
            'file_path': file_path,
            'file_size': os.path.getsize(file_path),
            'sheet_count': len(sheet_data),
            'sheets': {}
        }
        
        for sheet_name, df in sheet_data.items():
            # 基本结构分析
            structure_info = self.preprocessor.analyze_sheet_structure(df)
            
            # 语言检测
            detected_language = self.preprocessor.detect_language(df)
            
            # 逻辑单元分析
            logical_units = self.parser.parse_dataframe(df, sheet_name)
            
            sheet_analysis = {
                **structure_info,
                'detected_language': detected_language,
                'logical_units_count': len(logical_units),
                'unit_types': {}
            }
            
            # 统计单元类型
            if logical_units:
                for unit in logical_units:
                    unit_type = unit.unit_type.value
                    sheet_analysis['unit_types'][unit_type] = sheet_analysis['unit_types'].get(unit_type, 0) + 1
            
            analysis['sheets'][sheet_name] = sheet_analysis
        
        return analysis
