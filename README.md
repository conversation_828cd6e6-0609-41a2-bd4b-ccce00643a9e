# MCP Semantic Splitter

一个基于模型上下文协议(MCP)的智能Excel表格语义分割服务器。

## 功能特性

- **多维度语义分析**: 结合语义、布局、格式、数据类型等多种特征进行智能分割
- **多工作表支持**: 自动处理Excel文件中的多个工作表
- **语言自适应**: 自动检测文档语言并选择最适合的语义模型
- **结构化输出**: 提供JSON格式的结构化分割结果
- **MCP协议兼容**: 完全兼容Model Context Protocol标准

## 核心算法

### 五阶段处理流程

1. **预处理与环境配置**: 多工作表处理、语言识别、模型选择
2. **结构化解析**: 宏观布局分割、逻辑单元识别
3. **多维特征提取**: 语义、布局、格式、数据类型、单元类型特征
4. **智能边界评分**: 基于多维特征的断裂度计算和局部峰值检测
5. **分段生成与后处理**: 片段生成、小片段合并、结构化输出

## 安装

```bash
# 使用uv安装
uv add mcp-semantic-splitter

# 或使用pip安装
pip install mcp-semantic-splitter
```

## 使用方法

### 作为MCP服务器

```bash
mcp-semantic-splitter
```

### 编程接口

```python
from mcp_semantic_splitter import SemanticSplitter

splitter = SemanticSplitter()
result = splitter.split_excel("path/to/your/file.xlsx")
```

## 配置

可以通过配置文件或环境变量调整分割参数：

- `SEMANTIC_MODEL`: 语义模型名称
- `MIN_SEGMENT_SIZE`: 最小片段大小
- `BOUNDARY_THRESHOLD`: 边界检测阈值

## 开发

```bash
# 克隆仓库
git clone https://github.com/your-org/mcp-semantic-splitter.git
cd mcp-semantic-splitter

# 安装开发依赖
uv sync --dev

# 运行测试
uv run pytest

# 代码格式化
uv run black .
uv run isort .
```

## 许可证

MIT License