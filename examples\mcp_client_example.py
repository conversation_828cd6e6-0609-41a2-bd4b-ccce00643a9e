"""
MCP客户端示例

演示如何作为MCP客户端使用表格语义分割服务。
"""

import asyncio
import json
import tempfile
import os
import pandas as pd
from mcp.client.session import ClientSession
from mcp.client.stdio import stdio_client
import subprocess
import sys
import types
from mcp.client.stdio import StdioServerParameters

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

server_config = StdioServerParameters(
    command=sys.executable,
    args=["-m", "mcp_semantic_splitter.server"],
    cwd=PROJECT_ROOT,
    # 其余参数不用写，自动用默认值
)


async def create_sample_file():
    """创建示例Excel文件"""
    data = {
        "Product Name": [
            "iPhone 14",
            "Samsung Galaxy S23",
            "Google Pixel 7",
            "",
            "Sales Summary",
        ],
        "Price": [999, 899, 699, "", ""],
        "Units Sold": [1500, 1200, 800, "", "Total: 3500"],
        "Revenue": [1498500, 1078800, 559200, "", "Total: $3,136,500"],
        "Category": ["Smartphone", "Smartphone", "Smartphone", "", ""],
    }

    with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_file:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(tmp_file.name, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="Sales_Data", index=False)
        return tmp_file.name


async def test_mcp_tools():
    """测试MCP工具"""
    # 创建示例文件
    test_file = await create_sample_file()

    try:

        # 使用正确的方式启动MCP客户端
        async with stdio_client(server_config) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化会话
                await session.initialize()

                print("=== MCP Semantic Splitter 客户端示例 ===\n")

                # 1. 列出可用工具
                print("1. 列出可用工具:")
                tools = await session.list_tools()
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")
                print()

                # 2. 列出可用资源
                print("2. 列出可用资源:")
                resources = await session.list_resources()
                for resource in resources.resources:
                    print(f"   - {resource.name}: {resource.description}")
                print()

                # 3. 读取帮助资源
                print("3. 读取使用帮助:")
                help_content = await session.read_resource("help://usage")
                print(help_content.contents[0].text[:500] + "...\n")

                # 4. 配置分割器
                print("4. 配置分割器:")
                config_result = await session.call_tool(
                    "configure_splitter",
                    {
                        "config": {
                            "min_segment_size": 3,
                            "boundary_threshold": 0.6,
                            "semantic_weight": 0.4,
                            "layout_weight": 0.3,
                            "format_weight": 0.2,
                            "datatype_weight": 0.05,
                            "unittype_weight": 0.05,
                        }
                    },
                )
                print("配置结果:")
                print(config_result.content[0].text[:300] + "...\n")

                # 5. 分析文件结构
                print("5. 分析文件结构:")
                analysis_result = await session.call_tool(
                    "analyze_file_structure", {"file_path": test_file}
                )
                print("结构分析结果:")
                print(analysis_result.content[0].text[:500] + "...\n")

                # 6. 分割Excel文件
                print("6. 分割Excel文件:")
                split_result = await session.call_tool(
                    "split_excel_file",
                    {
                        "file_path": test_file,
                        "sheet_name": "Sales_Data",
                        "config": {"min_segment_size": 2, "boundary_threshold": 0.5},
                    },
                )
                print("分割结果:")
                print(split_result.content[0].text[:800] + "...\n")

                # 7. 读取当前配置
                print("7. 读取当前配置:")
                current_config = await session.read_resource("config://current")
                config_data = json.loads(current_config.contents[0].text)
                print("当前配置:")
                for key, value in config_data.items():
                    print(f"   {key}: {value}")

    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)


async def batch_processing_example():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===\n")

    # 创建多个测试文件
    test_files = []

    try:
        # 创建不同类型的测试数据
        datasets = [
            {
                "name": "employees.xlsx",
                "data": {
                    "Name": ["Alice", "Bob", "Charlie"],
                    "Department": ["Engineering", "Marketing", "Sales"],
                    "Salary": [75000, 65000, 55000],
                },
            },
            {
                "name": "products.xlsx",
                "data": {
                    "Product": ["Laptop", "Mouse", "Keyboard"],
                    "Price": [999.99, 29.99, 79.99],
                    "Stock": [50, 200, 150],
                },
            },
        ]

        # 创建文件
        for dataset in datasets:
            with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_file:
                df = pd.DataFrame(dataset["data"])
                with pd.ExcelWriter(tmp_file.name, engine="openpyxl") as writer:
                    df.to_excel(writer, sheet_name="Data", index=False)
                test_files.append((tmp_file.name, dataset["name"]))

        async with stdio_client(server_config) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()

                # 批量处理文件
                for file_path, file_name in test_files:
                    print(f"处理文件: {file_name}")

                    # 分析结构
                    analysis = await session.call_tool(
                        "analyze_file_structure", {"file_path": file_path}
                    )

                    # 分割文件
                    split_result = await session.call_tool(
                        "split_excel_file", {"file_path": file_path}
                    )

                    print(
                        f"   分割完成，结果长度: {len(split_result.content[0].text)} 字符"
                    )
                    print()

    finally:
        # 清理所有测试文件
        for file_path, _ in test_files:
            if os.path.exists(file_path):
                os.unlink(file_path)


async def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===\n")

    async with stdio_client(server_config) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            # 1. 测试不存在的文件
            print("1. 测试不存在的文件:")
            try:
                result = await session.call_tool(
                    "split_excel_file", {"file_path": "/nonexistent/file.xlsx"}
                )
                print(f"   结果: {result.content[0].text[:200]}...")
            except Exception as e:
                print(f"   错误: {str(e)}")
            print()

            # 2. 测试无效配置
            print("2. 测试无效配置:")
            try:
                result = await session.call_tool(
                    "configure_splitter",
                    {
                        "config": {
                            "semantic_weight": 0.8,  # 权重总和 > 1
                            "layout_weight": 0.3,
                            "format_weight": 0.2,
                            "datatype_weight": 0.1,
                            "unittype_weight": 0.1,
                        }
                    },
                )
                print(f"   结果: {result.content[0].text[:200]}...")
            except Exception as e:
                print(f"   错误: {str(e)}")
            print()

            # 3. 测试无效工具调用
            print("3. 测试无效工具调用:")
            try:
                result = await session.call_tool("nonexistent_tool", {})
                print(f"   结果: {result.content[0].text}")
            except Exception as e:
                print(f"   错误: {str(e)}")


async def main():
    """主函数"""
    try:
        await test_mcp_tools()
        await batch_processing_example()
        await error_handling_example()

        print("\n所有MCP客户端示例运行完成！")

    except Exception as e:
        print(f"运行MCP客户端示例时出错: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
