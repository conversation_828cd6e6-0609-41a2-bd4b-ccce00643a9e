"""
结构解析模块

负责将表格的原始网格结构转化为逻辑单元列表。
包括宏观布局分割和逻辑单元识别。
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from uuid import uuid4

from .models import (
    LogicalUnit, 
    UnitType, 
    CellPosition, 
    CellRange,
    SplitterConfig
)

logger = logging.getLogger(__name__)


class StructuralParser:
    """结构解析器"""
    
    def __init__(self, config: SplitterConfig):
        self.config = config
    
    def parse_dataframe(self, df: pd.DataFrame, sheet_name: str) -> List[LogicalUnit]:
        """
        解析DataFrame，生成逻辑单元列表
        
        Args:
            df: 要解析的DataFrame
            sheet_name: 工作表名称
            
        Returns:
            逻辑单元列表
        """
        if df.empty:
            return []
        
        logger.info(f"开始解析工作表 {sheet_name}，形状: {df.shape}")
        
        # 第一步：宏观布局分割，识别内容区块
        content_blocks = self._identify_content_blocks(df)
        logger.info(f"识别到 {len(content_blocks)} 个内容区块")
        
        # 第二步：在每个区块内识别逻辑单元
        logical_units = []
        for i, block in enumerate(content_blocks):
            block_units = self._identify_logical_units(block, f"{sheet_name}_block_{i}")
            logical_units.extend(block_units)
        
        logger.info(f"总共识别到 {len(logical_units)} 个逻辑单元")
        return logical_units
    
    def _identify_content_blocks(self, df: pd.DataFrame) -> List[pd.DataFrame]:
        """
        识别内容区块，使用连续的空行和空列作为分隔符
        
        Args:
            df: 原始DataFrame
            
        Returns:
            内容区块列表
        """
        # 识别空行
        empty_rows = []
        for i in range(len(df)):
            if df.iloc[i].isna().all() or (df.iloc[i] == '').all():
                empty_rows.append(i)
        
        # 识别空列
        empty_cols = []
        for col in df.columns:
            if df[col].isna().all() or (df[col] == '').all():
                empty_cols.append(col)
        
        # 基于空行分割
        row_blocks = self._split_by_empty_rows(df, empty_rows)
        
        # 在每个行块内基于空列进一步分割
        content_blocks = []
        for row_block in row_blocks:
            if not row_block.empty:
                col_blocks = self._split_by_empty_columns(row_block, empty_cols)
                content_blocks.extend(col_blocks)
        
        # 过滤掉空块
        content_blocks = [block for block in content_blocks if not block.empty]
        
        return content_blocks
    
    def _split_by_empty_rows(self, df: pd.DataFrame, empty_rows: List[int]) -> List[pd.DataFrame]:
        """
        基于空行分割DataFrame
        
        Args:
            df: 原始DataFrame
            empty_rows: 空行索引列表
            
        Returns:
            分割后的DataFrame列表
        """
        if not empty_rows:
            return [df]
        
        blocks = []
        start_row = 0
        
        for empty_row in empty_rows:
            if empty_row > start_row:
                block = df.iloc[start_row:empty_row].copy()
                if not block.empty:
                    blocks.append(block)
            start_row = empty_row + 1
        
        # 处理最后一个块
        if start_row < len(df):
            block = df.iloc[start_row:].copy()
            if not block.empty:
                blocks.append(block)
        
        return blocks
    
    def _split_by_empty_columns(self, df: pd.DataFrame, empty_cols: List[int]) -> List[pd.DataFrame]:
        """
        基于空列分割DataFrame
        
        Args:
            df: 原始DataFrame
            empty_cols: 空列索引列表
            
        Returns:
            分割后的DataFrame列表
        """
        # 找到在当前DataFrame中实际存在的空列
        actual_empty_cols = [col for col in empty_cols if col in df.columns]
        
        if not actual_empty_cols:
            return [df]
        
        blocks = []
        start_col = df.columns[0]
        
        for empty_col in sorted(actual_empty_cols):
            if empty_col > start_col:
                # 选择从start_col到empty_col之间的列
                col_range = [col for col in df.columns if start_col <= col < empty_col]
                if col_range:
                    block = df[col_range].copy()
                    if not block.empty:
                        blocks.append(block)
            start_col = empty_col + 1
        
        # 处理最后一个块
        remaining_cols = [col for col in df.columns if col >= start_col]
        if remaining_cols:
            block = df[remaining_cols].copy()
            if not block.empty:
                blocks.append(block)
        
        return blocks
    
    def _identify_logical_units(self, block: pd.DataFrame, block_id: str) -> List[LogicalUnit]:
        """
        在内容区块内识别逻辑单元
        
        Args:
            block: 内容区块DataFrame
            block_id: 区块标识符
            
        Returns:
            逻辑单元列表
        """
        # 分析区块结构
        structure_type = self._analyze_block_structure(block)
        
        if structure_type == "table":
            # 表格结构：每行作为一个逻辑单元
            return self._create_row_units(block, block_id)
        elif structure_type == "prose":
            # 文本结构：整个区块作为一个逻辑单元
            return self._create_block_unit(block, block_id)
        else:
            # 混合结构：尝试智能分割
            return self._create_mixed_units(block, block_id)
    
    def _analyze_block_structure(self, block: pd.DataFrame) -> str:
        """
        分析区块的结构类型
        
        Args:
            block: 内容区块
            
        Returns:
            结构类型：'table', 'prose', 'mixed'
        """
        rows, cols = block.shape
        
        # 计算数据密度
        non_empty_cells = 0
        total_cells = rows * cols
        
        for col in block.columns:
            for value in block[col]:
                if pd.notna(value) and value != '':
                    non_empty_cells += 1
        
        density = non_empty_cells / total_cells if total_cells > 0 else 0
        
        # 分析数据类型分布
        text_cells = 0
        numeric_cells = 0
        
        for col in block.columns:
            for value in block[col]:
                if pd.notna(value) and value != '':
                    if isinstance(value, (int, float)):
                        numeric_cells += 1
                    elif isinstance(value, str):
                        text_cells += 1
        
        # 决策逻辑
        if density > 0.7 and cols > 2:
            # 高密度且多列，可能是表格
            return "table"
        elif text_cells > numeric_cells * 2 and cols <= 2:
            # 文本为主且列数少，可能是文本段落
            return "prose"
        else:
            # 混合结构
            return "mixed"
    
    def _create_row_units(self, block: pd.DataFrame, block_id: str) -> List[LogicalUnit]:
        """
        创建行单元列表
        
        Args:
            block: 内容区块
            block_id: 区块标识符
            
        Returns:
            行单元列表
        """
        units = []
        
        for i, (idx, row) in enumerate(block.iterrows()):
            unit_id = f"{block_id}_row_{i}"
            
            # 计算单元格范围
            start_pos = CellPosition(row=idx, column=block.columns[0])
            end_pos = CellPosition(row=idx, column=block.columns[-1])
            cell_range = CellRange(start=start_pos, end=end_pos)
            
            # 创建逻辑单元
            unit = LogicalUnit(
                unit_id=unit_id,
                unit_type=UnitType.ROW,
                range=cell_range,
                content=row.to_dict(),
                metadata={
                    'row_index': idx,
                    'column_count': len(block.columns),
                    'non_empty_cells': sum(1 for v in row if pd.notna(v) and v != ''),
                }
            )
            units.append(unit)
        
        return units
    
    def _create_block_unit(self, block: pd.DataFrame, block_id: str) -> List[LogicalUnit]:
        """
        创建区块单元
        
        Args:
            block: 内容区块
            block_id: 区块标识符
            
        Returns:
            包含单个区块单元的列表
        """
        unit_id = f"{block_id}_block"
        
        # 计算单元格范围
        start_pos = CellPosition(row=block.index[0], column=block.columns[0])
        end_pos = CellPosition(row=block.index[-1], column=block.columns[-1])
        cell_range = CellRange(start=start_pos, end=end_pos)
        
        # 合并所有文本内容
        text_content = []
        for col in block.columns:
            for value in block[col]:
                if pd.notna(value) and value != '':
                    text_content.append(str(value))
        
        unit = LogicalUnit(
            unit_id=unit_id,
            unit_type=UnitType.BLOCK,
            range=cell_range,
            content=' '.join(text_content),
            metadata={
                'shape': block.shape,
                'text_length': len(' '.join(text_content)),
                'cell_count': block.shape[0] * block.shape[1],
            }
        )
        
        return [unit]
    
    def _create_mixed_units(self, block: pd.DataFrame, block_id: str) -> List[LogicalUnit]:
        """
        创建混合单元（智能分割）
        
        Args:
            block: 内容区块
            block_id: 区块标识符
            
        Returns:
            逻辑单元列表
        """
        # 对于混合结构，先尝试按行分割，如果行太少则按区块处理
        if len(block) <= 3:
            return self._create_block_unit(block, block_id)
        else:
            return self._create_row_units(block, block_id)
    
    def detect_special_units(self, df: pd.DataFrame) -> List[LogicalUnit]:
        """
        检测特殊单元（图表、图片等）
        
        注意：这是一个简化实现，实际应用中需要更复杂的检测逻辑
        
        Args:
            df: DataFrame
            
        Returns:
            特殊单元列表
        """
        special_units = []
        
        # 简单的启发式检测
        for i, row in df.iterrows():
            for j, value in enumerate(row):
                if isinstance(value, str):
                    value_lower = value.lower()
                    if any(keyword in value_lower for keyword in ['chart', '图表', 'graph', '图形']):
                        # 可能是图表引用
                        unit_id = f"chart_{uuid4().hex[:8]}"
                        start_pos = CellPosition(row=i, column=j)
                        end_pos = CellPosition(row=i, column=j)
                        cell_range = CellRange(start=start_pos, end=end_pos)
                        
                        unit = LogicalUnit(
                            unit_id=unit_id,
                            unit_type=UnitType.CHART,
                            range=cell_range,
                            content=value,
                            metadata={'detected_type': 'chart_reference'}
                        )
                        special_units.append(unit)
        
        return special_units
