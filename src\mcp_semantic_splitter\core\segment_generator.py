"""
分段生成器模块

根据检测到的边界生成最终的片段，并进行后处理优化。
"""

import logging
from typing import List, Dict, Any, Optional
import pandas as pd
from uuid import uuid4

from .models import (
    LogicalUnit, 
    Segment, 
    SegmentType,
    BoundaryScore,
    SplitterConfig
)
from .feature_extractor import FeatureExtractor

logger = logging.getLogger(__name__)


class SegmentGenerator:
    """分段生成器"""
    
    def __init__(self, config: SplitterConfig, feature_extractor: FeatureExtractor):
        self.config = config
        self.feature_extractor = feature_extractor
    
    def generate_segments(
        self, 
        units: List[LogicalUnit], 
        boundaries: List[BoundaryScore],
        sheet_name: str
    ) -> List[Segment]:
        """
        根据边界生成片段
        
        Args:
            units: 逻辑单元列表
            boundaries: 边界评分列表
            sheet_name: 工作表名称
            
        Returns:
            片段列表
        """
        if not units:
            return []
        
        logger.info(f"开始生成片段，{len(units)} 个单元，{len(boundaries)} 个边界")
        
        # 获取边界位置并排序
        boundary_positions = sorted([b.position for b in boundaries])
        
        # 生成初始片段
        segments = self._create_initial_segments(units, boundary_positions, sheet_name)
        
        # 后处理
        if self.config.merge_small_segments:
            segments = self._merge_small_segments(segments)
        
        # 分类片段类型
        segments = self._classify_segments(segments)
        
        # 生成片段内容
        segments = self._generate_segment_content(segments)
        
        logger.info(f"生成了 {len(segments)} 个片段")
        return segments
    
    def _create_initial_segments(
        self, 
        units: List[LogicalUnit], 
        boundary_positions: List[int],
        sheet_name: str
    ) -> List[Segment]:
        """
        创建初始片段
        
        Args:
            units: 逻辑单元列表
            boundary_positions: 边界位置列表
            sheet_name: 工作表名称
            
        Returns:
            初始片段列表
        """
        segments = []
        start_idx = 0
        
        # 添加边界位置的结束标记
        split_points = boundary_positions + [len(units)]
        
        for i, end_idx in enumerate(split_points):
            if end_idx > start_idx:
                # 创建片段
                segment_units = units[start_idx:end_idx]
                
                segment_id = f"{sheet_name}_segment_{i}"
                source_location = self._generate_source_location(segment_units, sheet_name)
                
                segment = Segment(
                    segment_id=segment_id,
                    segment_type=SegmentType.MIXED,  # 初始类型，后续会重新分类
                    source_location=source_location,
                    units=segment_units,
                    content=None,  # 后续生成
                    metadata={
                        'unit_count': len(segment_units),
                        'start_unit_id': segment_units[0].unit_id,
                        'end_unit_id': segment_units[-1].unit_id,
                    }
                )
                
                segments.append(segment)
                start_idx = end_idx
        
        return segments
    
    def _generate_source_location(self, units: List[LogicalUnit], sheet_name: str) -> str:
        """
        生成片段的源位置字符串
        
        Args:
            units: 逻辑单元列表
            sheet_name: 工作表名称
            
        Returns:
            源位置字符串，如 "Sheet1!A1:D10"
        """
        if not units:
            return f"{sheet_name}!A1:A1"
        
        # 找到最小和最大的行列位置
        min_row = min(unit.range.start.row for unit in units)
        max_row = max(unit.range.end.row for unit in units)
        min_col = min(unit.range.start.column for unit in units)
        max_col = max(unit.range.end.column for unit in units)
        
        # 转换为Excel格式
        start_cell = self._position_to_excel(min_row, min_col)
        end_cell = self._position_to_excel(max_row, max_col)
        
        return f"{sheet_name}!{start_cell}:{end_cell}"
    
    def _position_to_excel(self, row: int, col: int) -> str:
        """
        将行列位置转换为Excel格式
        
        Args:
            row: 行号（0索引）
            col: 列号（0索引）
            
        Returns:
            Excel格式的单元格位置，如 "A1"
        """
        # 转换列号为字母
        col_letter = ""
        col_num = col + 1  # 转换为1索引
        
        while col_num > 0:
            col_num -= 1
            col_letter = chr(col_num % 26 + ord('A')) + col_letter
            col_num //= 26
        
        return f"{col_letter}{row + 1}"  # 行号也转换为1索引
    
    def _merge_small_segments(self, segments: List[Segment]) -> List[Segment]:
        """
        合并小片段
        
        Args:
            segments: 原始片段列表
            
        Returns:
            合并后的片段列表
        """
        if len(segments) <= 1:
            return segments
        
        merged_segments = []
        i = 0
        
        while i < len(segments):
            current_segment = segments[i]
            
            # 检查当前片段是否太小
            if current_segment.total_cells < self.config.min_segment_size:
                # 寻找最佳合并目标
                merge_target = self._find_best_merge_target(current_segment, segments, i)
                
                if merge_target is not None:
                    # 执行合并
                    merged_segment = self._merge_segments(current_segment, merge_target)
                    
                    # 更新列表
                    if merge_target == i - 1:  # 与前一个合并
                        merged_segments[-1] = merged_segment
                    elif merge_target == i + 1:  # 与后一个合并
                        merged_segments.append(merged_segment)
                        i += 1  # 跳过下一个片段
                    else:
                        merged_segments.append(current_segment)
                else:
                    merged_segments.append(current_segment)
            else:
                merged_segments.append(current_segment)
            
            i += 1
        
        logger.info(f"小片段合并：{len(segments)} -> {len(merged_segments)} 个片段")
        return merged_segments
    
    def _find_best_merge_target(
        self, 
        small_segment: Segment, 
        all_segments: List[Segment], 
        current_index: int
    ) -> Optional[int]:
        """
        为小片段找到最佳合并目标
        
        Args:
            small_segment: 小片段
            all_segments: 所有片段列表
            current_index: 当前片段索引
            
        Returns:
            最佳合并目标的索引，如果没有则返回None
        """
        candidates = []
        
        # 检查前一个片段
        if current_index > 0:
            prev_segment = all_segments[current_index - 1]
            similarity = self._compute_segment_similarity(small_segment, prev_segment)
            candidates.append((current_index - 1, similarity))
        
        # 检查后一个片段
        if current_index < len(all_segments) - 1:
            next_segment = all_segments[current_index + 1]
            similarity = self._compute_segment_similarity(small_segment, next_segment)
            candidates.append((current_index + 1, similarity))
        
        # 选择相似度最高的
        if candidates:
            best_target, best_similarity = max(candidates, key=lambda x: x[1])
            return best_target
        
        return None
    
    def _compute_segment_similarity(self, segment1: Segment, segment2: Segment) -> float:
        """
        计算两个片段的相似度
        
        Args:
            segment1: 第一个片段
            segment2: 第二个片段
            
        Returns:
            相似度分数
        """
        # 简化实现：基于单元类型的相似度
        if not (segment1.units and segment2.units):
            return 0.0
        
        # 计算单元类型分布的相似度
        types1 = [unit.unit_type for unit in segment1.units]
        types2 = [unit.unit_type for unit in segment2.units]
        
        # 如果主要单元类型相同，则相似度较高
        if types1 and types2:
            main_type1 = max(set(types1), key=types1.count)
            main_type2 = max(set(types2), key=types2.count)
            
            if main_type1 == main_type2:
                return 0.8
            else:
                return 0.2
        
        return 0.0
    
    def _merge_segments(self, segment1: Segment, segment2: Segment) -> Segment:
        """
        合并两个片段
        
        Args:
            segment1: 第一个片段
            segment2: 第二个片段
            
        Returns:
            合并后的片段
        """
        # 合并单元列表
        merged_units = segment1.units + segment2.units
        
        # 生成新的ID和位置
        merged_id = f"merged_{uuid4().hex[:8]}"
        merged_location = self._generate_source_location(
            merged_units, 
            segment1.source_location.split('!')[0]  # 提取工作表名
        )
        
        # 合并元数据
        merged_metadata = {
            **segment1.metadata,
            **segment2.metadata,
            'merged_from': [segment1.segment_id, segment2.segment_id],
            'unit_count': len(merged_units),
        }
        
        merged_segment = Segment(
            segment_id=merged_id,
            segment_type=SegmentType.MIXED,  # 重新分类
            source_location=merged_location,
            units=merged_units,
            content=None,  # 重新生成
            metadata=merged_metadata
        )
        
        return merged_segment
    
    def _classify_segments(self, segments: List[Segment]) -> List[Segment]:
        """
        分类片段类型
        
        Args:
            segments: 片段列表
            
        Returns:
            分类后的片段列表
        """
        for segment in segments:
            segment.segment_type = self._determine_segment_type(segment)
        
        return segments
    
    def _determine_segment_type(self, segment: Segment) -> SegmentType:
        """
        确定片段类型
        
        Args:
            segment: 片段
            
        Returns:
            片段类型
        """
        if not segment.units:
            return SegmentType.MIXED
        
        # 统计单元类型
        unit_types = [unit.unit_type for unit in segment.units]
        type_counts = {}
        for unit_type in unit_types:
            type_counts[unit_type] = type_counts.get(unit_type, 0) + 1
        
        # 获取主要类型
        main_type = max(type_counts.keys(), key=lambda k: type_counts[k])
        main_ratio = type_counts[main_type] / len(unit_types)
        
        # 根据主要类型和比例确定片段类型
        if main_ratio > 0.8:  # 80%以上是同一类型
            if main_type.value == 'row':
                return SegmentType.DATA_TABLE
            elif main_type.value == 'block':
                return SegmentType.PROSE
            elif main_type.value == 'chart':
                return SegmentType.CHART
        
        # 特殊判断：如果只有一个单元且是文本，可能是标题
        if len(segment.units) == 1 and main_type.value == 'block':
            unit = segment.units[0]
            if isinstance(unit.content, str) and len(unit.content.split()) <= 10:
                return SegmentType.SECTION_TITLE
        
        return SegmentType.MIXED
    
    def _generate_segment_content(self, segments: List[Segment]) -> List[Segment]:
        """
        生成片段内容
        
        Args:
            segments: 片段列表
            
        Returns:
            包含内容的片段列表
        """
        for segment in segments:
            if self.config.include_content:
                segment.content = self._extract_segment_content(segment)
        
        return segments
    
    def _extract_segment_content(self, segment: Segment) -> Any:
        """
        提取片段内容
        
        Args:
            segment: 片段
            
        Returns:
            片段内容
        """
        if not segment.units:
            return None
        
        if segment.segment_type == SegmentType.DATA_TABLE:
            # 表格数据：合并为DataFrame
            rows_data = []
            for unit in segment.units:
                if isinstance(unit.content, dict):
                    rows_data.append(unit.content)
            
            if rows_data:
                return pd.DataFrame(rows_data)
        
        elif segment.segment_type in [SegmentType.PROSE, SegmentType.SECTION_TITLE]:
            # 文本内容：连接所有文本
            texts = []
            for unit in segment.units:
                if isinstance(unit.content, str):
                    texts.append(unit.content)
                elif isinstance(unit.content, dict):
                    # 从字典中提取文本
                    unit_texts = [str(v) for v in unit.content.values() if v]
                    texts.extend(unit_texts)
            
            return ' '.join(texts)
        
        else:
            # 其他类型：返回原始内容列表
            return [unit.content for unit in segment.units]
