"""
基本使用示例

演示如何使用MCP Semantic Splitter进行表格分割。
"""

import pandas as pd
import tempfile
import os
from mcp_semantic_splitter import SemanticSplitter, SplitterConfig


def create_sample_excel():
    """创建示例Excel文件"""
    # 创建示例数据
    data = {
        'Employee ID': [1, 2, 3, '', 'Summary'],
        'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '', ''],
        'Department': ['Engineering', 'Marketing', 'Sales', '', 'Total Employees: 3'],
        'Salary': [75000, 65000, 55000, '', ''],
        'Start Date': ['2020-01-15', '2019-06-01', '2021-03-10', '', '']
    }
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df = pd.DataFrame(data)
        
        # 写入Excel文件
        with pd.ExcelWriter(tmp_file.name, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Employees', index=False)
            
            # 添加第二个工作表
            summary_data = {
                'Metric': ['Total Employees', 'Average Salary', 'Departments'],
                'Value': [3, 65000, 3],
                'Notes': ['Active employees only', 'Excluding bonuses', 'Engineering, Marketing, Sales']
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        return tmp_file.name


def basic_splitting_example():
    """基本分割示例"""
    print("=== 基本分割示例 ===")
    
    # 创建示例文件
    excel_file = create_sample_excel()
    
    try:
        # 创建分割器（使用默认配置）
        splitter = SemanticSplitter()
        
        # 分割Excel文件
        print(f"正在分割文件: {excel_file}")
        results = splitter.split_excel(excel_file)
        
        # 显示结果
        for result in results:
            print(f"\n工作表: {result.sheet_name}")
            print(f"处理时间: {result.processing_time:.2f}秒")
            print(f"片段数量: {len(result.segments)}")
            print(f"状态: {result.metadata.get('status', 'unknown')}")
            
            for i, segment in enumerate(result.segments):
                print(f"  片段 {i+1}: {segment.segment_type.value} ({segment.total_cells} 单元格)")
                print(f"    位置: {segment.source_location}")
                print(f"    逻辑单元数: {len(segment.units)}")
    
    finally:
        # 清理临时文件
        if os.path.exists(excel_file):
            os.unlink(excel_file)


def custom_config_example():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建示例文件
    excel_file = create_sample_excel()
    
    try:
        # 创建自定义配置
        config = SplitterConfig(
            min_segment_size=2,  # 最小片段大小
            boundary_threshold=0.7,  # 较高的边界阈值
            semantic_weight=0.5,  # 增加语义权重
            layout_weight=0.3,
            format_weight=0.1,
            datatype_weight=0.05,
            unittype_weight=0.05,
            merge_small_segments=True,  # 合并小片段
            include_content=True  # 包含内容
        )
        
        # 创建分割器
        splitter = SemanticSplitter(config)
        
        # 只处理特定工作表
        results = splitter.split_excel(excel_file, sheet_name="Employees")
        
        # 显示详细结果
        for result in results:
            print(f"\n工作表: {result.sheet_name}")
            print(f"配置: 阈值={config.boundary_threshold}, 最小大小={config.min_segment_size}")
            
            for segment in result.segments:
                print(f"\n片段: {segment.segment_id}")
                print(f"  类型: {segment.segment_type.value}")
                print(f"  位置: {segment.source_location}")
                print(f"  单元数: {len(segment.units)}")
                
                # 显示内容（如果有）
                if hasattr(segment, 'content') and segment.content is not None:
                    if isinstance(segment.content, pd.DataFrame):
                        print(f"  内容: DataFrame ({segment.content.shape})")
                        print(f"    前几行: {segment.content.head(2).to_dict('records')}")
                    else:
                        content_str = str(segment.content)
                        if len(content_str) > 100:
                            content_str = content_str[:100] + "..."
                        print(f"  内容: {content_str}")
    
    finally:
        # 清理临时文件
        if os.path.exists(excel_file):
            os.unlink(excel_file)


def dataframe_splitting_example():
    """DataFrame分割示例"""
    print("\n=== DataFrame分割示例 ===")
    
    # 创建示例DataFrame
    data = {
        'Product': ['Laptop', 'Mouse', 'Keyboard', '', 'Category Summary'],
        'Price': [999.99, 29.99, 79.99, '', ''],
        'Stock': [50, 200, 150, '', 'Total Items: 400'],
        'Category': ['Electronics', 'Accessories', 'Accessories', '', '']
    }
    df = pd.DataFrame(data)
    
    print("原始DataFrame:")
    print(df)
    
    # 创建分割器
    splitter = SemanticSplitter()
    
    # 分割DataFrame
    result = splitter.split_dataframe(df, "Products")
    
    print(f"\n分割结果:")
    print(f"片段数量: {len(result.segments)}")
    
    for i, segment in enumerate(result.segments):
        print(f"\n片段 {i+1}:")
        print(f"  类型: {segment.segment_type.value}")
        print(f"  逻辑单元数: {len(segment.units)}")
        print(f"  总单元格数: {segment.total_cells}")
        
        # 显示每个逻辑单元
        for j, unit in enumerate(segment.units):
            print(f"    单元 {j+1}: {unit.unit_type.value} - {unit.range}")


def file_analysis_example():
    """文件分析示例"""
    print("\n=== 文件分析示例 ===")
    
    # 创建示例文件
    excel_file = create_sample_excel()
    
    try:
        # 创建分割器
        splitter = SemanticSplitter()
        
        # 分析文件结构（不进行分割）
        analysis = splitter.analyze_file_structure(excel_file)
        
        print(f"文件: {analysis['file_path']}")
        print(f"大小: {analysis['file_size'] / 1024:.1f} KB")
        print(f"工作表数量: {analysis['sheet_count']}")
        
        for sheet_name, sheet_info in analysis['sheets'].items():
            print(f"\n工作表: {sheet_name}")
            print(f"  形状: {sheet_info.get('shape', 'N/A')}")
            print(f"  数据密度: {sheet_info.get('density', 0):.2%}")
            print(f"  检测语言: {sheet_info.get('detected_language', 'unknown')}")
            print(f"  逻辑单元数: {sheet_info.get('logical_units_count', 0)}")
            
            if 'unit_types' in sheet_info:
                print("  单元类型分布:")
                for unit_type, count in sheet_info['unit_types'].items():
                    print(f"    {unit_type}: {count}")
    
    finally:
        # 清理临时文件
        if os.path.exists(excel_file):
            os.unlink(excel_file)


def main():
    """主函数"""
    print("MCP Semantic Splitter 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        basic_splitting_example()
        custom_config_example()
        dataframe_splitting_example()
        file_analysis_example()
        
        print("\n所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
